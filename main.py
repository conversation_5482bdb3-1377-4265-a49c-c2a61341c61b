# main.py - Nova Tools Only (No Conversation Handling)
from core import nova_tools
from config.nova_config import NOVA_NAME

def show_available_tools():
    """Display all available Nova tools"""
    print(f"\n{'='*60}")
    print(f"🛠️  {NOVA_NAME} - Available Tools")
    print(f"{'='*60}")
    print()

    print("🌤️  WEATHER TOOLS:")
    print("   • execute_weather_tool(user_input) - Get current weather or forecast")
    print("   • Example: execute_weather_tool('weather in London')")
    print()

    print("⏰ TIME & DATE TOOLS:")
    print("   • execute_time_tool() - Get current time")
    print("   • execute_date_tool() - Get current date")
    print()

    print("🔍 SEARCH & SCRAPING TOOLS:")
    print("   • execute_search_tool(query) - Search the web")
    print("   • execute_scraping_tool(url) - Scrape content from a webpage")
    print("   • Example: execute_search_tool('latest AI news')")
    print("   • Example: execute_scraping_tool('https://example.com')")
    print()

    print("📝 REMINDER & PLAN TOOLS:")
    print("   • execute_reminder_tool('add', data) - Add reminder")
    print("   • execute_reminder_tool('list') - List reminders")
    print("   • execute_reminder_tool('remove', reminder_id) - Remove reminder")
    print("   • execute_plan_tool('add', data) - Add plan")
    print("   • execute_plan_tool('list') - List plans")
    print()

    print("🔊 VOLUME TOOLS:")
    print("   • execute_volume_tool('get') - Get current volume")
    print("   • execute_volume_tool('set', level) - Set volume (0-100)")
    print("   • execute_volume_tool('mute') - Mute volume")
    print("   • execute_volume_tool('unmute') - Unmute volume")
    print()

    print("💻 APPLICATION TOOLS:")
    print("   • execute_app_tool('open', app_name) - Open application")
    print("   • execute_app_tool('close', app_name) - Close application")
    print("   • execute_app_tool('list') - List running apps")
    print()

    print("🖥️  SYSTEM TOOLS:")
    print("   • execute_system_tool('screenshot') - Take screenshot")
    print("   • execute_system_tool('lock') - Lock computer")
    print("   • execute_system_tool('shutdown') - Shutdown computer")
    print("   • execute_system_tool('restart') - Restart computer")
    print()

    print("🧠 KNOWLEDGE TOOLS:")
    print("   • execute_knowledge_tool('add', data) - Add knowledge")
    print("   • execute_knowledge_tool('search', query) - Search knowledge")
    print("   • execute_knowledge_tool('recent') - Get recent knowledge")
    print()

    print("🔧 UTILITY TOOLS:")
    print("   • perform_web_search(query) - Raw web search")
    print("   • extract_city_from_input(text) - Extract city from text")
    print()

    print(f"{'='*60}")
    print("💡 Usage: Import nova_tools and call any function directly")
    print("   Example: from core import nova_tools")
    print("           result = nova_tools.execute_weather_tool('weather in Paris')")
    print(f"{'='*60}")

def interactive_tool_test():
    """Interactive tool testing mode"""
    print("\n🧪 Interactive Tool Testing Mode")
    print("Type 'help' for available commands, 'quit' to exit")
    print("-" * 50)

    while True:
        try:
            user_input = input("\nTool Test > ").strip()

            if user_input.lower() in ['quit', 'exit', 'q']:
                print("👋 Goodbye!")
                break
            elif user_input.lower() == 'help':
                show_available_tools()
                continue
            elif not user_input:
                continue

            # Test some basic tools
            if 'weather' in user_input.lower():
                result = nova_tools.execute_weather_tool(user_input)
                print(f"🌤️  Weather Result: {result}")
            elif 'time' in user_input.lower():
                result = nova_tools.execute_time_tool()
                print(f"⏰ Time Result: {result}")
            elif 'date' in user_input.lower():
                result = nova_tools.execute_date_tool()
                print(f"📅 Date Result: {result}")
            elif 'search' in user_input.lower():
                query = user_input.replace('search', '').strip()
                if query:
                    result = nova_tools.execute_search_tool(query)
                    print(f"🔍 Search Results: {len(result)} results found")
                    for i, res in enumerate(result[:3], 1):
                        print(f"   {i}. {res['title']} - {res['source']}")
                else:
                    print("❌ Please specify a search query")
            elif 'volume' in user_input.lower():
                result = nova_tools.execute_volume_tool('get')
                print(f"🔊 Volume Result: {result}")
            else:
                print("❓ Unknown command. Type 'help' for available tools.")

        except KeyboardInterrupt:
            print("\n👋 Goodbye!")
            break
        except Exception as e:
            print(f"❌ Error: {e}")

def conversation_mode():
    """Interactive conversation mode with DeepSeek R1"""
    print(f"\n💬 {NOVA_NAME} Conversation Mode")
    print("Powered by DeepSeek R1 Free Model")
    print("Type 'quit' to exit, 'tools' to see available tools")
    print("-" * 50)

    while True:
        try:
            user_input = input(f"\nYou: ").strip()

            if user_input.lower() in ['quit', 'exit', 'bye']:
                print(f"\n{NOVA_NAME}: Goodbye! 👋")
                break
            elif user_input.lower() == 'tools':
                show_available_tools()
                continue
            elif not user_input:
                continue

            # Process input through Nova
            print(f"\n{NOVA_NAME}: ", end="", flush=True)
            response = nova_tools.process_user_input(user_input)
            print(response)

        except KeyboardInterrupt:
            print(f"\n\n{NOVA_NAME}: Goodbye! 👋")
            break
        except Exception as e:
            print(f"\n❌ Error: {e}")

def main():
    """Main function - choose between tools info or conversation"""
    print(f"\n🌟 Welcome to {NOVA_NAME}")
    print("Powered by DeepSeek R1 Free Model")
    print("=" * 50)

    print("\nChoose an option:")
    print("1. Start conversation with Nova")
    print("2. View available tools")
    print("3. Interactive tool testing")

    choice = input("\nEnter your choice (1-3): ").strip()

    if choice == "1":
        conversation_mode()
    elif choice == "2":
        show_available_tools()
        print("\n💡 You can now import and use Nova tools in your own code!")
        print("   Example: from core import nova_tools")
        print("           result = nova_tools.execute_weather_tool('weather today')")
    elif choice == "3":
        interactive_tool_test()
    else:
        print("Invalid choice. Starting conversation mode...")
        conversation_mode()

if __name__ == "__main__":
    main()
