# telegram_bot.py
from telegram import Update
from telegram.ext import <PERSON><PERSON><PERSON><PERSON>, CommandHandler, MessageHandler, ContextTypes, filters
from core import nova_memory, nova_tools
from core.nova_voice import <PERSON>Voice
from config.api_keys import TELEGRAM_BOT_TOKEN
from config.nova_config import NOVA_NAME, USER_NAME

# Initialize voice system for Telegram (optional)
tts_engine = NovaVoice()

async def start(update: Update, context: ContextTypes.DEFAULT_TYPE):
    """Handle /start command"""
    welcome_message = f"""🌟 **{NOVA_NAME} is Online!**

Hello! I'm {NOVA_NAME}, your AI companion. I can help you with:

🌤️ **Weather** - Ask about weather anywhere
⏰ **Time & Date** - Current time, dates, countdowns
📝 **Reminders** - Set and manage reminders
📋 **Plans** - Save and organize your plans
🔊 **System Control** - Volume, apps, screenshots
🧠 **Knowledge** - Remember and search information
🌐 **Web Search** - Find latest information online

Just chat with me naturally! For example:
• "What's the weather like?"
• "Remind me to call mom at 3pm"
• "Set volume to 50"
• "Open calculator"

Ready to help! ✨"""

    await update.message.reply_text(welcome_message, parse_mode='Markdown')

async def handle_message(update: Update, context: ContextTypes.DEFAULT_TYPE):
    """Handle all text messages"""
    user_input = update.message.text
    user_name = update.effective_user.first_name or "User"

    try:
        # Handle special commands
        if user_input.lower() == "reset":
            nova_memory.clear_memory()
            await update.message.reply_text("🧠 Memory cleared! Fresh start! ✨")
            return

        elif user_input.lower() in ["help", "/help"]:
            help_text = f"""🆘 **{NOVA_NAME} Help**

**Available Commands:**
• Weather: "weather in London", "forecast"
• Time: "what time is it", "date today"
• Reminders: "remind me to...", "list reminders"
• Plans: "save plan: trip details", "list plans"
• Volume: "set volume 50", "mute", "unmute"
• Apps: "open calculator", "close chrome"
• System: "screenshot", "lock computer"
• Knowledge: "remember this info", "search knowledge"
• Web: "search for latest news on..."

Just talk to me naturally! 💬"""

            await update.message.reply_text(help_text, parse_mode='Markdown')
            return

        # Process the message through Nova's enhanced system
        # First check if it's a tool request and get raw tool data
        tool_type, tool_data = nova_tools.get_tool_data(user_input)

        if tool_type and tool_data:
            # This was a tool request - process through DeepSeek for conversational response
            memory = nova_memory.load_memory()

            # Customize system prompt for Telegram
            if memory and memory[0]["role"] == "system":
                memory[0]["content"] = f"You are {NOVA_NAME}, a warm AI companion for {user_name}. You're chatting via Telegram, so keep responses concise but friendly. Use emojis naturally."

            nova_reply = nova_tools.process_with_deepseek(user_input, tool_type, tool_data, memory)

            # Save to memory
            nova_memory.save_memory(user_input, nova_reply)
        else:
            # Regular conversation
            # Load memory and get response
            memory = nova_memory.load_memory()

            # Customize system prompt for Telegram
            if memory and memory[0]["role"] == "system":
                memory[0]["content"] = f"You are {NOVA_NAME}, a warm AI companion for {user_name}. You're chatting via Telegram, so keep responses concise but friendly. Use emojis naturally."

            memory.append({"role": "user", "content": user_input})
            nova_reply = nova_tools.get_chat_response(memory)

            # Save to memory
            nova_memory.save_memory(user_input, nova_reply)

        # Send response (split if too long for Telegram)
        if len(nova_reply) > 4096:  # Telegram message limit
            # Split into chunks
            chunks = [nova_reply[i:i+4096] for i in range(0, len(nova_reply), 4096)]
            for chunk in chunks:
                await update.message.reply_text(chunk)
        else:
            await update.message.reply_text(nova_reply)

        # Save conversation context to knowledge base
        try:
            from core.nova_knowledge import save_context
            save_context("telegram_chat", f"{user_name}: {user_input} | {NOVA_NAME}: {nova_reply}")
        except:
            pass

    except Exception as e:
        print(f"❌ Error in handle_message: {e}")
        error_message = f"🔧 Oops! I encountered an issue: {str(e)}\n\nTry rephrasing your request or use /help for guidance."
        try:
            await update.message.reply_text(error_message)
        except Exception as reply_error:
            print(f"❌ Failed to send error message: {reply_error}")

async def handle_photo(update: Update, context: ContextTypes.DEFAULT_TYPE):
    """Handle photo messages"""
    await update.message.reply_text("📸 I can see you sent a photo! While I can't analyze images yet, I'm here to help with text-based requests. 😊")

async def handle_voice(update: Update, context: ContextTypes.DEFAULT_TYPE):
    """Handle voice messages"""
    await update.message.reply_text("🎤 I received your voice message! I can't process audio yet, but feel free to type your message and I'll be happy to help! 💬")

def main():
    """Main function to run the Telegram bot"""
    print(f"🤖 Starting {NOVA_NAME} Telegram Bot...")

    # Create application
    app = ApplicationBuilder().token(TELEGRAM_BOT_TOKEN).build()

    # Add handlers
    app.add_handler(CommandHandler("start", start))
    app.add_handler(CommandHandler("help", handle_message))
    app.add_handler(MessageHandler(filters.TEXT & ~filters.COMMAND, handle_message))
    app.add_handler(MessageHandler(filters.PHOTO, handle_photo))
    app.add_handler(MessageHandler(filters.VOICE, handle_voice))

    print(f"✅ {NOVA_NAME} Telegram Bot is running!")
    print("Press Ctrl+C to stop the bot.")

    # Start polling
    app.run_polling()

if __name__ == "__main__":
    main()
