# telegram_bot.py - Nova Telegram Bot with DeepSeek R1
import asyncio
import logging
from telegram import Update
from telegram.ext import Application, CommandHandler, MessageHandler, filters, ContextTypes
from core import nova_tools
from core.nova_logger import log_conversation, log_error, log_debug
from config.nova_config import NOVA_NAME
from config.api_keys import TELEGRAM_BOT_TOKEN

# Configure logging
logging.basicConfig(
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    level=logging.INFO
)
logger = logging.getLogger(__name__)

class NovaTelegramBot:
    def __init__(self):
        self.application = None
        self.conversation_history = {}  # Store conversation history per user
        
    async def start_command(self, update: Update, context: ContextTypes.DEFAULT_TYPE):
        """Handle /start command"""
        user_name = update.effective_user.first_name or "there"
        welcome_message = f"""🌟 Hello {user_name}! I'm {NOVA_NAME}, your AI assistant powered by DeepSeek R1.

I can help you with:
🌤️ Weather information
⏰ Time and date
🔍 Web searches and scraping
📝 Reminders and plans
🔊 System controls (if running on your computer)
🧠 Knowledge management

Just chat with me naturally! Type /help for more commands."""

        await update.message.reply_text(welcome_message)
        log_conversation(f"[TELEGRAM] {user_name}: /start", welcome_message)

    async def help_command(self, update: Update, context: ContextTypes.DEFAULT_TYPE):
        """Handle /help command"""
        help_message = f"""🛠️ {NOVA_NAME} Commands:

/start - Start conversation with Nova
/help - Show this help message
/tools - List available tools
/weather [city] - Get weather for a city
/time - Get current time
/search [query] - Search the web
/scrape [url] - Scrape content from a webpage

Or just chat naturally! I understand:
• "What's the weather like?"
• "What time is it?"
• "Search for AI news"
• "Scrape https://example.com"
• And much more!"""

        await update.message.reply_text(help_message)

    async def tools_command(self, update: Update, context: ContextTypes.DEFAULT_TYPE):
        """Handle /tools command"""
        tools_message = f"""🛠️ {NOVA_NAME} Available Tools:

🌤️ **Weather Tools**
• Current weather and forecasts

⏰ **Time & Date Tools**
• Current time and date information

🔍 **Search & Scraping Tools**
• Web search and content extraction

📝 **Reminder & Plan Tools**
• Add, list, and manage reminders/plans

🔊 **Volume Tools** (if on your computer)
• Control system volume

💻 **Application Tools** (if on your computer)
• Open/close applications

🖥️ **System Tools** (if on your computer)
• Screenshots, lock, shutdown, restart

🧠 **Knowledge Tools**
• Store and retrieve information

Just ask me naturally! For example:
"What's the weather in London?"
"What time is it?"
"Search for Python tutorials"
"Scrape https://news.ycombinator.com"
"""

        await update.message.reply_text(tools_message)

    async def weather_command(self, update: Update, context: ContextTypes.DEFAULT_TYPE):
        """Handle /weather command"""
        if context.args:
            city = " ".join(context.args)
            user_input = f"What's the weather in {city}?"
        else:
            user_input = "What's the weather like?"
        
        await self.process_message(update, user_input)

    async def time_command(self, update: Update, context: ContextTypes.DEFAULT_TYPE):
        """Handle /time command"""
        await self.process_message(update, "What time is it?")

    async def search_command(self, update: Update, context: ContextTypes.DEFAULT_TYPE):
        """Handle /search command"""
        if context.args:
            query = " ".join(context.args)
            user_input = f"Search for {query}"
            await self.process_message(update, user_input)
        else:
            await update.message.reply_text("Please provide a search query. Example: /search AI news")

    async def scrape_command(self, update: Update, context: ContextTypes.DEFAULT_TYPE):
        """Handle /scrape command"""
        if context.args:
            url = context.args[0]
            user_input = f"Scrape {url}"
            await self.process_message(update, user_input)
        else:
            await update.message.reply_text("Please provide a URL to scrape. Example: /scrape https://example.com")

    async def process_message(self, update: Update, user_input: str = None):
        """Process user message through Nova"""
        try:
            user_id = update.effective_user.id
            user_name = update.effective_user.first_name or "User"
            message_text = user_input or update.message.text

            log_debug("TELEGRAM", f"Processing message from {user_name}: {message_text}")

            # Send typing indicator
            await update.message.chat.send_action(action="typing")

            # Get conversation history for this user
            if user_id not in self.conversation_history:
                self.conversation_history[user_id] = []

            # Add context for follow-up messages
            context_message = message_text
            if len(self.conversation_history[user_id]) > 0:
                # If this is a follow-up message, add context
                recent_messages = self.conversation_history[user_id][-2:]  # Last 2 exchanges
                if recent_messages:
                    context_parts = []
                    for msg in recent_messages:
                        if msg['role'] == 'user':
                            context_parts.append(f"Previously you asked: {msg['content']}")
                        elif msg['role'] == 'assistant':
                            context_parts.append(f"I responded: {msg['content'][:100]}...")

                    if context_parts:
                        context_message = f"Context: {' '.join(context_parts)} Now you're asking: {message_text}"

            # Process through Nova with context
            nova_reply = nova_tools.process_user_input(context_message)

            # Store conversation history
            self.conversation_history[user_id].append({"role": "user", "content": message_text})
            self.conversation_history[user_id].append({"role": "assistant", "content": nova_reply})

            # Keep only last 10 messages to prevent memory bloat
            if len(self.conversation_history[user_id]) > 10:
                self.conversation_history[user_id] = self.conversation_history[user_id][-10:]

            # Split long messages for Telegram
            if len(nova_reply) > 4000:
                # Split into chunks
                chunks = [nova_reply[i:i+4000] for i in range(0, len(nova_reply), 4000)]
                for chunk in chunks:
                    await update.message.reply_text(chunk)
            else:
                await update.message.reply_text(nova_reply)

            # Log conversation
            log_conversation(f"[TELEGRAM] {user_name}: {message_text}", nova_reply)

        except Exception as e:
            error_msg = f"Sorry, I encountered an error: {str(e)}"
            await update.message.reply_text(error_msg)
            log_error("TELEGRAM", str(e), message_text if 'message_text' in locals() else "Unknown")

    async def handle_message(self, update: Update, context: ContextTypes.DEFAULT_TYPE):
        """Handle regular text messages"""
        await self.process_message(update)

    def setup_handlers(self):
        """Setup bot command and message handlers"""
        # Command handlers
        self.application.add_handler(CommandHandler("start", self.start_command))
        self.application.add_handler(CommandHandler("help", self.help_command))
        self.application.add_handler(CommandHandler("tools", self.tools_command))
        self.application.add_handler(CommandHandler("weather", self.weather_command))
        self.application.add_handler(CommandHandler("time", self.time_command))
        self.application.add_handler(CommandHandler("search", self.search_command))
        self.application.add_handler(CommandHandler("scrape", self.scrape_command))
        
        # Message handler for regular text
        self.application.add_handler(MessageHandler(filters.TEXT & ~filters.COMMAND, self.handle_message))

    def start_bot(self):
        """Start the Telegram bot"""
        try:
            # Create application
            self.application = Application.builder().token(TELEGRAM_BOT_TOKEN).build()

            # Setup handlers
            self.setup_handlers()

            # Start the bot
            print(f"🤖 {NOVA_NAME} Telegram Bot starting...")
            print("Bot is running! Send /start to begin.")
            print("Press Ctrl+C to stop the bot.")

            # Run the bot
            self.application.run_polling(allowed_updates=Update.ALL_TYPES)

        except Exception as e:
            print(f"❌ Error starting Telegram bot: {e}")
            log_error("TELEGRAM_STARTUP", str(e))

def main():
    """Main function to run the Telegram bot"""
    # Check if token is configured
    if not TELEGRAM_BOT_TOKEN or TELEGRAM_BOT_TOKEN == "YOUR_TELEGRAM_BOT_TOKEN_HERE":
        print("❌ Telegram bot token not configured!")
        print("Please add your bot token to config/api_keys.py")
        print("Get a token from @BotFather on Telegram")
        return

    # Create and start bot
    bot = NovaTelegramBot()

    try:
        bot.start_bot()
    except KeyboardInterrupt:
        print(f"\n👋 {NOVA_NAME} Telegram Bot stopped.")
    except Exception as e:
        print(f"❌ Bot error: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
