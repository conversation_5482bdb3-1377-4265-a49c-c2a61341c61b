# demo_tools.py - Demonstration of Nova Tools
"""
This script demonstrates how to use Nova's tools directly
without any conversation handling or AI model integration.
"""

from core import nova_tools

def demo_weather_tools():
    """Demonstrate weather tools"""
    print("\n🌤️  WEATHER TOOLS DEMO")
    print("-" * 30)
    
    try:
        # Current weather
        print("Getting current weather...")
        weather = nova_tools.execute_weather_tool("weather in London")
        print(f"Result: {weather}")
        
        # Weather forecast
        print("\nGetting weather forecast...")
        forecast = nova_tools.execute_weather_tool("forecast for tomorrow in Paris")
        print(f"Result: {forecast}")
        
    except Exception as e:
        print(f"Error: {e}")

def demo_time_tools():
    """Demonstrate time and date tools"""
    print("\n⏰ TIME & DATE TOOLS DEMO")
    print("-" * 30)
    
    try:
        # Current time
        print("Getting current time...")
        time_result = nova_tools.execute_time_tool()
        print(f"Result: {time_result}")
        
        # Current date
        print("\nGetting current date...")
        date_result = nova_tools.execute_date_tool()
        print(f"Result: {date_result}")
        
    except Exception as e:
        print(f"Error: {e}")

def demo_search_tools():
    """Demonstrate search tools"""
    print("\n🔍 SEARCH TOOLS DEMO")
    print("-" * 30)
    
    try:
        # Web search
        print("Searching for 'Python programming tips'...")
        search_results = nova_tools.execute_search_tool("Python programming tips")
        print(f"Found {len(search_results)} results:")
        
        for i, result in enumerate(search_results[:3], 1):
            print(f"  {i}. {result['title']}")
            print(f"     Source: {result['source']}")
            print(f"     Snippet: {result['snippet'][:100]}...")
            print()
        
    except Exception as e:
        print(f"Error: {e}")

def demo_volume_tools():
    """Demonstrate volume tools"""
    print("\n🔊 VOLUME TOOLS DEMO")
    print("-" * 30)
    
    try:
        # Get current volume
        print("Getting current volume...")
        volume = nova_tools.execute_volume_tool("get")
        print(f"Current volume: {volume}")
        
        # Note: Uncomment these to actually change volume
        # print("\nSetting volume to 50...")
        # nova_tools.execute_volume_tool("set", 50)
        
        # print("\nMuting volume...")
        # nova_tools.execute_volume_tool("mute")
        
    except Exception as e:
        print(f"Error: {e}")

def demo_app_tools():
    """Demonstrate application tools"""
    print("\n💻 APPLICATION TOOLS DEMO")
    print("-" * 30)
    
    try:
        # List running apps
        print("Getting list of running applications...")
        apps = nova_tools.execute_app_tool("list", None)
        print(f"Running apps: {apps}")
        
        # Note: Uncomment to actually open/close apps
        # print("\nOpening Notepad...")
        # nova_tools.execute_app_tool("open", "notepad")
        
    except Exception as e:
        print(f"Error: {e}")

def demo_knowledge_tools():
    """Demonstrate knowledge tools"""
    print("\n🧠 KNOWLEDGE TOOLS DEMO")
    print("-" * 30)
    
    try:
        # Add knowledge
        print("Adding knowledge...")
        add_result = nova_tools.execute_knowledge_tool("add", "Python is a great programming language")
        print(f"Add result: {add_result}")
        
        # Search knowledge
        print("\nSearching knowledge...")
        search_result = nova_tools.execute_knowledge_tool("search", "Python")
        print(f"Search result: {search_result}")
        
        # Get recent knowledge
        print("\nGetting recent knowledge...")
        recent = nova_tools.execute_knowledge_tool("recent")
        print(f"Recent knowledge: {recent}")
        
    except Exception as e:
        print(f"Error: {e}")

def demo_utility_functions():
    """Demonstrate utility functions"""
    print("\n🔧 UTILITY FUNCTIONS DEMO")
    print("-" * 30)
    
    try:
        # Extract city from input
        test_input = "What's the weather like in Tokyo today?"
        city = nova_tools.extract_city_from_input(test_input)
        print(f"Input: '{test_input}'")
        print(f"Extracted city: {city}")
        
        # Raw web search
        print("\nPerforming raw web search...")
        raw_results = nova_tools.perform_web_search("AI news", max_results=2)
        print(f"Raw search found {len(raw_results)} results:")
        for result in raw_results:
            print(f"  - {result['title']} ({result['source']})")
        
    except Exception as e:
        print(f"Error: {e}")

def main():
    """Run all tool demonstrations"""
    print("🛠️  NOVA TOOLS DEMONSTRATION")
    print("=" * 50)
    print("This script shows how to use Nova's tools directly")
    print("without any conversation handling or AI integration.")
    print("=" * 50)
    
    # Run all demos
    demo_weather_tools()
    demo_time_tools()
    demo_search_tools()
    demo_volume_tools()
    demo_app_tools()
    demo_knowledge_tools()
    demo_utility_functions()
    
    print("\n" + "=" * 50)
    print("✅ Tool demonstration complete!")
    print("💡 You can now use these tools in your own projects")
    print("   by importing: from core import nova_tools")
    print("=" * 50)

if __name__ == "__main__":
    main()
