# test_api_connection.py
"""
Simple test to verify API connection to DeepSeek R1 model
"""

import requests
import json
from config import nova_config as conf
from config.api_keys import OPENROUTER_API_KEY

def test_api_connection():
    """Test basic API connection"""
    print("🧪 Testing API Connection to DeepSeek R1...")
    
    # Check API key
    if not OPENROUTER_API_KEY or OPENROUTER_API_KEY == "YOUR_OPENROUTER_API_KEY_HERE":
        print("❌ API key not configured!")
        return False
    
    print(f"✅ API key configured: {OPENROUTER_API_KEY[:10]}...{OPENROUTER_API_KEY[-5:]}")
    
    # Test request
    headers = {
        "Authorization": f"Bearer {OPENROUTER_API_KEY}",
        "HTTP-Referer": conf.WEBSITE_URL,
        "X-Title": conf.APP_NAME,
        "Content-Type": "application/json"
    }
    
    payload = {
        "model": conf.DEEPSEEK_MODEL,
        "messages": [
            {"role": "system", "content": "You are <PERSON>, a helpful AI assistant."},
            {"role": "user", "content": "Hello! Can you respond with just 'API test successful'?"}
        ],
        "temperature": 0.8,
        "max_tokens": 50
    }
    
    print(f"🔗 Testing model: {conf.DEEPSEEK_MODEL}")
    print(f"🌐 API URL: {conf.OPENROUTER_URL}")
    
    try:
        response = requests.post(conf.OPENROUTER_URL, headers=headers, json=payload, timeout=30)
        
        print(f"📊 Response status: {response.status_code}")
        print(f"📋 Response headers: {dict(response.headers)}")
        
        if response.status_code == 200:
            print(f"📄 Raw response (first 500 chars): {response.text[:500]}")
            
            try:
                response_data = response.json()
                print(f"🔑 Response keys: {list(response_data.keys())}")
                
                if 'choices' in response_data and len(response_data['choices']) > 0:
                    choice = response_data['choices'][0]
                    if 'message' in choice and 'content' in choice['message']:
                        content = choice['message']['content']
                        print(f"✅ API Response: {content}")
                        return True
                    else:
                        print("❌ Missing message/content in response")
                        return False
                else:
                    print("❌ No choices in response")
                    return False
                    
            except json.JSONDecodeError as e:
                print(f"❌ JSON decode error: {e}")
                print(f"📄 Full response text: {response.text}")
                return False
        else:
            print(f"❌ HTTP error {response.status_code}: {response.text}")
            return False
            
    except requests.exceptions.Timeout:
        print("❌ Request timed out")
        return False
    except requests.exceptions.ConnectionError as e:
        print(f"❌ Connection error: {e}")
        return False
    except Exception as e:
        print(f"❌ Unexpected error: {e}")
        return False

def test_fallback_models():
    """Test fallback models"""
    print("\n🧪 Testing Fallback Models...")
    
    for model in conf.FALLBACK_MODELS:
        print(f"\n🔗 Testing {model}...")
        
        headers = {
            "Authorization": f"Bearer {OPENROUTER_API_KEY}",
            "HTTP-Referer": conf.WEBSITE_URL,
            "X-Title": conf.APP_NAME,
            "Content-Type": "application/json"
        }
        
        payload = {
            "model": model,
            "messages": [
                {"role": "user", "content": "Hello"}
            ],
            "temperature": 0.7,
            "max_tokens": 20
        }
        
        try:
            response = requests.post(conf.OPENROUTER_URL, headers=headers, json=payload, timeout=15)
            
            if response.status_code == 200:
                try:
                    response_data = response.json()
                    if 'choices' in response_data and len(response_data['choices']) > 0:
                        print(f"✅ {model} working")
                    else:
                        print(f"⚠️  {model} responded but no choices")
                except:
                    print(f"⚠️  {model} responded but JSON error")
            else:
                print(f"❌ {model} failed: {response.status_code}")
                
        except Exception as e:
            print(f"❌ {model} error: {e}")

if __name__ == "__main__":
    print("🌟 Nova AI Agent - API Connection Test")
    print("=" * 50)
    
    success = test_api_connection()
    
    if success:
        print("\n🎉 Primary model test successful!")
        test_fallback_models()
    else:
        print("\n⚠️  Primary model test failed. Checking fallbacks...")
        test_fallback_models()
    
    print("\n" + "=" * 50)
    print("Test completed. Check results above.")
