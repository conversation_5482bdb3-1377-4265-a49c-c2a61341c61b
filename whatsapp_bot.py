# whatsapp_bot.py
"""
WhatsApp Integration for Nova AI

IMPORTANT NOTES:
1. WhatsApp doesn't have an official API for personal accounts
2. This implementation uses unofficial methods that may violate WhatsApp's Terms of Service
3. Use at your own risk - WhatsApp may ban accounts using unofficial automation
4. For production use, consider WhatsApp Business API instead

SETUP INSTRUCTIONS:
1. Install Chrome browser
2. Run this script and scan QR code with your phone
3. Keep the browser window open while the bot runs

ALTERNATIVES:
- WhatsApp Business API (official, requires approval)
- Twilio WhatsApp API (paid service)
- Use Telegram instead (recommended)
"""

import time
import os
from selenium import webdriver
from selenium.webdriver.common.by import By
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC
from selenium.webdriver.chrome.service import Service
from selenium.webdriver.chrome.options import Options
from webdriver_manager.chrome import ChromeDriverManager
from core import nova_memory, nova_tools
from config.nova_config import NOVA_NAME, USER_NAME

class WhatsAppBot:
    def __init__(self):
        self.driver = None
        self.is_logged_in = False
        self.last_message_count = 0
        
    def setup_driver(self):
        """Setup Chrome WebDriver for WhatsApp Web"""
        try:
            chrome_options = Options()
            chrome_options.add_argument("--user-data-dir=./whatsapp_session")  # Save session
            chrome_options.add_argument("--no-sandbox")
            chrome_options.add_argument("--disable-dev-shm-usage")
            # chrome_options.add_argument("--headless")  # Uncomment for headless mode
            
            service = Service(ChromeDriverManager().install())
            self.driver = webdriver.Chrome(service=service, options=chrome_options)
            
            print("✅ Chrome WebDriver initialized")
            return True
            
        except Exception as e:
            print(f"❌ Failed to setup WebDriver: {e}")
            return False
    
    def login_to_whatsapp(self):
        """Login to WhatsApp Web"""
        try:
            print("🌐 Opening WhatsApp Web...")
            self.driver.get("https://web.whatsapp.com")
            
            # Wait for QR code or main interface
            print("📱 Please scan the QR code with your phone...")
            print("⏳ Waiting for login...")
            
            # Wait for either QR code or chat interface
            WebDriverWait(self.driver, 60).until(
                lambda driver: driver.find_elements(By.CSS_SELECTOR, "[data-testid='chat-list']") or
                              driver.find_elements(By.CSS_SELECTOR, "canvas")
            )
            
            # Check if we're logged in
            if self.driver.find_elements(By.CSS_SELECTOR, "[data-testid='chat-list']"):
                print("✅ Successfully logged into WhatsApp!")
                self.is_logged_in = True
                return True
            else:
                print("⏳ Still waiting for QR code scan...")
                # Wait a bit more for login
                WebDriverWait(self.driver, 120).until(
                    EC.presence_of_element_located((By.CSS_SELECTOR, "[data-testid='chat-list']"))
                )
                print("✅ Successfully logged into WhatsApp!")
                self.is_logged_in = True
                return True
                
        except Exception as e:
            print(f"❌ Failed to login to WhatsApp: {e}")
            return False
    
    def get_unread_messages(self):
        """Get unread messages from WhatsApp"""
        try:
            if not self.is_logged_in:
                return []
            
            # Find unread chats (those with notification badges)
            unread_chats = self.driver.find_elements(
                By.CSS_SELECTOR, 
                "[data-testid='cell-frame-container'] [data-testid='icon-unread-count']"
            )
            
            messages = []
            
            for chat in unread_chats[:5]:  # Limit to 5 unread chats
                try:
                    # Click on the chat
                    chat_container = chat.find_element(By.XPATH, "./ancestor::div[@data-testid='cell-frame-container']")
                    chat_container.click()
                    
                    time.sleep(1)  # Wait for chat to load
                    
                    # Get chat name
                    chat_name = self.driver.find_element(
                        By.CSS_SELECTOR, 
                        "[data-testid='conversation-header'] span[title]"
                    ).get_attribute("title")
                    
                    # Get recent messages
                    message_elements = self.driver.find_elements(
                        By.CSS_SELECTOR,
                        "[data-testid='msg-container']"
                    )
                    
                    # Get last few messages
                    for msg_elem in message_elements[-3:]:  # Last 3 messages
                        try:
                            # Check if it's an incoming message (not sent by us)
                            if not msg_elem.find_elements(By.CSS_SELECTOR, "[data-testid='msg-meta'] [data-testid='msg-check']"):
                                continue
                            
                            # Get message text
                            text_elem = msg_elem.find_element(By.CSS_SELECTOR, "[data-testid='conversation-compose-box-input']")
                            message_text = text_elem.text.strip()
                            
                            if message_text:
                                messages.append({
                                    'chat': chat_name,
                                    'message': message_text,
                                    'timestamp': time.time()
                                })
                                
                        except:
                            continue
                            
                except Exception as e:
                    print(f"Error processing chat: {e}")
                    continue
            
            return messages
            
        except Exception as e:
            print(f"❌ Error getting messages: {e}")
            return []
    
    def send_message(self, chat_name, message):
        """Send a message to a specific chat"""
        try:
            # Search for the chat
            search_box = self.driver.find_element(
                By.CSS_SELECTOR,
                "[data-testid='chat-list-search'] input"
            )
            search_box.clear()
            search_box.send_keys(chat_name)
            
            time.sleep(2)
            
            # Click on the first result
            first_result = self.driver.find_element(
                By.CSS_SELECTOR,
                "[data-testid='cell-frame-container']"
            )
            first_result.click()
            
            time.sleep(1)
            
            # Type message
            message_box = self.driver.find_element(
                By.CSS_SELECTOR,
                "[data-testid='conversation-compose-box-input']"
            )
            message_box.clear()
            message_box.send_keys(message)
            
            # Send message
            send_button = self.driver.find_element(
                By.CSS_SELECTOR,
                "[data-testid='compose-btn-send']"
            )
            send_button.click()
            
            print(f"✅ Message sent to {chat_name}")
            return True
            
        except Exception as e:
            print(f"❌ Failed to send message: {e}")
            return False
    
    def process_message(self, chat_name, message_text):
        """Process incoming message and generate response"""
        try:
            print(f"📨 Processing message from {chat_name}: {message_text}")
            
            # Check if it's a tool request and get raw tool data
            tool_type, tool_data = nova_tools.get_tool_data(message_text)

            if tool_type and tool_data:
                # Process tool data through DeepSeek for conversational response
                memory = nova_memory.load_memory()

                # Customize for WhatsApp
                if memory and memory[0]["role"] == "system":
                    memory[0]["content"] = f"You are {NOVA_NAME}, a helpful AI assistant chatting via WhatsApp with {chat_name}. Keep responses concise for mobile. Use emojis naturally."

                nova_reply = nova_tools.process_with_deepseek(message_text, tool_type, tool_data, memory)

                # Save to memory
                nova_memory.save_memory(message_text, nova_reply)
            else:
                # Regular conversation using simple method
                memory = nova_memory.load_memory()

                # Create conversation context
                conversation_context = ""
                for msg in memory[-3:]:  # Last 3 messages for context
                    if msg['role'] == 'user':
                        conversation_context += f"{chat_name}: {msg['content']}\n"
                    elif msg['role'] == 'assistant':
                        conversation_context += f"{NOVA_NAME}: {msg['content']}\n"

                whatsapp_prompt = f"""You are {NOVA_NAME}, a helpful AI assistant chatting via WhatsApp with {chat_name}. Keep responses concise for mobile. Use emojis naturally.

Recent conversation:
{conversation_context}
{chat_name}: {message_text}

Please respond naturally as {NOVA_NAME}."""

                nova_reply = nova_tools.get_simple_deepseek_response(whatsapp_prompt)

                # Fallback if simple method fails
                if not nova_reply:
                    nova_reply = "I'm having some trouble right now. Could you try asking again? 🤔"

                # Save to memory
                nova_memory.save_memory(message_text, nova_reply)
            
            # Send response
            return self.send_message(chat_name, nova_reply)
            
        except Exception as e:
            print(f"❌ Error processing message: {e}")
            # Try to send an error message back
            try:
                self.send_message(chat_name, f"🔧 Sorry, I encountered an issue: {str(e)}")
            except:
                pass
            return False
    
    def run_bot(self):
        """Main bot loop"""
        print(f"🤖 Starting {NOVA_NAME} WhatsApp Bot...")
        
        if not self.setup_driver():
            return
        
        if not self.login_to_whatsapp():
            return
        
        print("🔄 Bot is running! Monitoring for messages...")
        print("Press Ctrl+C to stop the bot.")
        
        try:
            while True:
                # Check for new messages
                messages = self.get_unread_messages()
                
                for msg in messages:
                    self.process_message(msg['chat'], msg['message'])
                
                # Wait before checking again
                time.sleep(5)
                
        except KeyboardInterrupt:
            print("\n🛑 Bot stopped by user")
        except Exception as e:
            print(f"❌ Bot error: {e}")
        finally:
            if self.driver:
                self.driver.quit()

def main():
    """Main function"""
    print("🚨 WARNING: WhatsApp Bot uses unofficial methods!")
    print("This may violate WhatsApp's Terms of Service.")
    print("Use at your own risk. Consider using Telegram instead.")
    print()
    
    response = input("Do you want to continue? (yes/no): ").lower()
    if response != 'yes':
        print("Bot cancelled. Consider using telegram_bot.py instead!")
        return
    
    bot = WhatsAppBot()
    bot.run_bot()

if __name__ == "__main__":
    main()
