[2025-07-09 18:45:23] INFO - SESSION: Nova AI Agent session started
[2025-07-09 18:45:23] INFO - SESSION: ============================================================
[2025-07-09 18:45:38] DEBUG - API: Trying model: deepseek/deepseek-r1:free
[2025-07-09 18:45:38] DEBUG - API: Making API call with 2 messages
[2025-07-09 18:45:50] DEBUG - API: Response status: 200
[2025-07-09 18:45:50] DEBUG - API: Response data keys: ['id', 'provider', 'model', 'object', 'created', 'choices', 'usage']
[2025-07-09 18:45:50] INFO - API: Model: deepseek/deepseek-r1:free, Status: 200
[2025-07-09 18:45:50] DEBUG - API: Response preview: Hello! Yes, I can hear you. 😊 How can I assist you today?
[2025-07-09 18:50:34] INFO - SESSION: Nova AI Agent session started
[2025-07-09 18:50:34] INFO - SESSION: ============================================================
[2025-07-09 18:52:31] DEBUG - CHAT: Processing user input: Hello Nova, how are you?
[2025-07-09 18:52:31] DEBUG - CHAT: No tool detected, proceeding with regular conversation
[2025-07-09 18:52:31] DEBUG - CHAT: Skipping knowledge search for simple greeting/short input
[2025-07-09 18:52:31] ERROR - MAIN_LOOP: Expecting value: line 1 column 1 (char 0)
[2025-07-09 18:52:31] ERROR - MAIN_LOOP_CONTEXT: Hello Nova, how are you?
[2025-07-09 18:52:59] DEBUG - CHAT: Processing user input: what is quantum computing?
[2025-07-09 18:52:59] DEBUG - CHAT: No tool detected, proceeding with regular conversation
[2025-07-09 18:53:00] DEBUG - CHAT: No relevant knowledge found, proceeding normally
[2025-07-09 18:53:00] ERROR - KNOWLEDGE_SEARCH: Expecting value: line 1 column 1 (char 0)
[2025-07-09 18:53:00] ERROR - KNOWLEDGE_SEARCH_CONTEXT: what is quantum computing?
[2025-07-09 18:53:00] ERROR - MAIN_LOOP: Expecting value: line 1 column 1 (char 0)
[2025-07-09 18:53:00] ERROR - MAIN_LOOP_CONTEXT: what is quantum computing?
[2025-07-09 18:53:22] DEBUG - CHAT: Processing user input: whats your name?
[2025-07-09 18:53:22] DEBUG - CHAT: No tool detected, proceeding with regular conversation
[2025-07-09 18:53:22] DEBUG - CHAT: Skipping knowledge search for simple greeting/short input
[2025-07-09 18:53:22] ERROR - MAIN_LOOP: Expecting value: line 1 column 1 (char 0)
[2025-07-09 18:53:22] ERROR - MAIN_LOOP_CONTEXT: whats your name?
[2025-07-09 19:04:06] INFO - SESSION: Nova AI Agent session started
[2025-07-09 19:04:06] INFO - SESSION: ============================================================
[2025-07-09 19:04:18] DEBUG - CHAT: Processing user input: Hello Nova, how are you?
[2025-07-09 19:04:18] DEBUG - CHAT: No tool detected, proceeding with regular conversation
[2025-07-09 19:04:18] DEBUG - CHAT: Skipping knowledge search for simple greeting/short input
[2025-07-09 19:04:18] ERROR - MAIN_LOOP: Expecting value: line 1 column 1 (char 0)
[2025-07-09 19:04:18] ERROR - MAIN_LOOP_CONTEXT: Hello Nova, how are you?
[2025-07-09 19:05:10] DEBUG - CHAT: Processing user input: hi
[2025-07-09 19:05:10] DEBUG - CHAT: No tool detected, proceeding with regular conversation
[2025-07-09 19:05:10] DEBUG - CHAT: Skipping knowledge search for simple greeting/short input
[2025-07-09 19:05:14] ERROR - MAIN_LOOP: Expecting value: line 1 column 1 (char 0)
[2025-07-09 19:05:14] ERROR - MAIN_LOOP_CONTEXT: hi
[2025-07-09 19:22:37] INFO - SESSION: Nova AI Agent session started
[2025-07-09 19:22:37] INFO - SESSION: ============================================================
[2025-07-09 19:23:04] DEBUG - CHAT: Processing user input: Hi nova, how you?
[2025-07-09 19:23:04] DEBUG - CHAT: No tool detected, proceeding with regular conversation
[2025-07-09 19:23:04] DEBUG - CHAT: Skipping knowledge search for simple greeting/short input
[2025-07-09 19:23:04] ERROR - MAIN_LOOP: Expecting value: line 1 column 1 (char 0)
[2025-07-09 19:23:04] ERROR - MAIN_LOOP_CONTEXT: Hi nova, how you?
[2025-07-09 19:31:30] INFO - SESSION: Nova AI Agent session started
[2025-07-09 19:31:30] INFO - SESSION: ============================================================
[2025-07-09 19:31:30] DEBUG - SIMPLE_API: Making simple request to deepseek/deepseek-r1:free
[2025-07-09 19:32:12] DEBUG - SIMPLE_API: Making simple request to deepseek/deepseek-r1:free
[2025-07-09 19:32:34] DEBUG - SIMPLE_API: Making simple request to deepseek/deepseek-r1:free
[2025-07-09 19:32:51] DEBUG - SIMPLE_API: Making simple request to deepseek/deepseek-r1:free
[2025-07-09 19:34:02] DEBUG - DEEPSEEK_TOOL: Processing weather_current with custom prompt
[2025-07-09 19:34:02] DEBUG - DEEPSEEK_PROMPT: Custom prompt: The user asked: "What's the weather like?"

I just checked the current weather and here's what I found: Temperature: 20°C, Sunny, London

Please respond naturally as Nova, a friendly AI assistant, inc...
[2025-07-09 19:34:02] DEBUG - SIMPLE_API: Making simple request to deepseek/deepseek-r1:free
[2025-07-09 19:38:48] INFO - SESSION: Nova AI Agent session started
[2025-07-09 19:38:48] INFO - SESSION: ============================================================
[2025-07-09 19:39:08] DEBUG - CHAT: Processing user input: Hi Nova, how you?
[2025-07-09 19:39:08] DEBUG - CHAT: No tool detected, proceeding with regular conversation
[2025-07-09 19:39:08] DEBUG - CHAT: Skipping knowledge search for simple greeting/short input
[2025-07-09 19:39:08] ERROR - MAIN_LOOP: Expecting value: line 1 column 1 (char 0)
[2025-07-09 19:39:08] ERROR - MAIN_LOOP_CONTEXT: Hi Nova, how you?
[2025-07-09 19:47:29] INFO - SESSION: Nova AI Agent session started
[2025-07-09 19:47:29] INFO - SESSION: ============================================================
[2025-07-09 19:47:29] DEBUG - SIMPLE_API: Making simple request to deepseek/deepseek-r1:free
[2025-07-09 19:47:45] DEBUG - SIMPLE_API: Making simple request to deepseek/deepseek-r1:free
[2025-07-09 19:48:13] DEBUG - SIMPLE_API: Making simple request to deepseek/deepseek-r1:free
[2025-07-09 19:53:55] INFO - SESSION: Nova AI Agent session started
[2025-07-09 19:53:56] INFO - SESSION: ============================================================
[2025-07-09 19:55:20] DEBUG - CHAT: Processing user input: Hello Nova, how are you?
[2025-07-09 19:55:20] DEBUG - CHAT: No tool detected, proceeding with regular conversation
[2025-07-09 19:55:20] DEBUG - CHAT: Skipping knowledge search for simple greeting/short input
[2025-07-09 19:55:20] ERROR - MAIN_LOOP: Expecting value: line 1 column 1 (char 0)
[2025-07-09 19:55:20] ERROR - MAIN_LOOP_CONTEXT: Hello Nova, how are you?
[2025-07-09 20:12:59] INFO - SESSION: Nova AI Agent session started
[2025-07-09 20:12:59] INFO - SESSION: ============================================================
[2025-07-09 20:12:59] DEBUG - SIMPLE_API: Making simple request to deepseek/deepseek-r1:free
[2025-07-09 20:13:12] ERROR - SIMPLE_API: Error: HTTPSConnectionPool(host='openrouter.ai', port=443): Max retries exceeded with url: /api/v1/chat/completions (Caused by NameResolutionError("<urllib3.connection.HTTPSConnection object at 0x000002D193A3CAD0>: Failed to resolve 'openrouter.ai' ([Errno 11001] getaddrinfo failed)"))
[2025-07-09 20:15:59] INFO - SESSION: Nova AI Agent session started
[2025-07-09 20:15:59] INFO - SESSION: ============================================================
[2025-07-09 20:16:25] DEBUG - CHAT: Processing user input: Hello Nova, how you?
[2025-07-09 20:16:25] DEBUG - CHAT: No tool detected, proceeding with regular conversation
[2025-07-09 20:16:25] DEBUG - CHAT: Skipping knowledge search for simple greeting/short input
[2025-07-09 20:16:25] ERROR - MAIN_LOOP: Expecting value: line 1 column 1 (char 0)
[2025-07-09 20:16:25] ERROR - MAIN_LOOP_CONTEXT: Hello Nova, how you?
[2025-07-09 20:16:31] DEBUG - CHAT: Processing user input: Hello Nova
[2025-07-09 20:16:31] DEBUG - CHAT: No tool detected, proceeding with regular conversation
[2025-07-09 20:16:31] DEBUG - CHAT: Skipping knowledge search for simple greeting/short input
[2025-07-09 20:16:31] ERROR - MAIN_LOOP: Expecting value: line 1 column 1 (char 0)
[2025-07-09 20:16:31] ERROR - MAIN_LOOP_CONTEXT: Hello Nova
[2025-07-09 20:46:20] INFO - SESSION: Nova AI Agent session started
[2025-07-09 20:46:20] INFO - SESSION: ============================================================
[2025-07-09 20:46:20] DEBUG - CONVERSATION: Regular conversation
[2025-07-09 20:46:20] DEBUG - DEEPSEEK_API: Making request to deepseek/deepseek-r1:free
[2025-07-09 20:46:30] DEBUG - CONVERSATION: Regular conversation
[2025-07-09 20:46:30] DEBUG - DEEPSEEK_API: Making request to deepseek/deepseek-r1:free
[2025-07-09 20:46:40] DEBUG - CONVERSATION: Tool detected: weather_current
[2025-07-09 20:46:40] DEBUG - DEEPSEEK_API: Making request to deepseek/deepseek-r1:free
[2025-07-09 20:46:55] DEBUG - CONVERSATION: Tool detected: current_time
[2025-07-09 20:46:55] DEBUG - DEEPSEEK_API: Making request to deepseek/deepseek-r1:free
[2025-07-09 20:52:44] INFO - SESSION: Nova AI Agent session started
[2025-07-09 20:52:44] INFO - SESSION: ============================================================
[2025-07-09 20:52:44] DEBUG - CONVERSATION: Regular conversation
[2025-07-09 20:52:44] DEBUG - DEEPSEEK_API: Making request to deepseek/deepseek-r1:free
[2025-07-09 20:53:24] DEBUG - CONVERSATION: Regular conversation
[2025-07-09 20:53:24] DEBUG - DEEPSEEK_API: Making request to deepseek/deepseek-r1:free
[2025-07-09 20:55:37] DEBUG - CONVERSATION: Regular conversation
[2025-07-09 20:55:37] DEBUG - DEEPSEEK_API: Making request to deepseek/deepseek-r1:free
[2025-07-09 20:57:29] DEBUG - CONVERSATION: Regular conversation
[2025-07-09 20:57:29] DEBUG - DEEPSEEK_API: Making request to deepseek/deepseek-r1:free
[2025-07-09 20:58:03] DEBUG - CONVERSATION: Regular conversation
[2025-07-09 20:58:03] DEBUG - DEEPSEEK_API: Making request to deepseek/deepseek-r1:free
[2025-07-09 21:09:40] INFO - SESSION: Nova AI Agent session started
[2025-07-09 21:09:40] INFO - SESSION: ============================================================
[2025-07-09 21:09:40] DEBUG - CONVERSATION: Regular conversation
[2025-07-09 21:09:40] DEBUG - DEEPSEEK_API: Making request to deepseek/deepseek-r1:free
[2025-07-09 21:09:46] DEBUG - CONVERSATION: Regular conversation
[2025-07-09 21:09:46] DEBUG - DEEPSEEK_API: Making request to deepseek/deepseek-r1:free
[2025-07-09 21:09:54] DEBUG - CONVERSATION: Tool detected: weather_current
[2025-07-09 21:09:54] DEBUG - DEEPSEEK_API: Making request to deepseek/deepseek-r1:free
[2025-07-09 21:10:12] ERROR - DEEPSEEK_API: Error: ('Connection aborted.', ConnectionResetError(10054, 'An existing connection was forcibly closed by the remote host', None, 10054, None))
[2025-07-09 21:10:12] DEBUG - CONVERSATION: Tool detected: current_time
[2025-07-09 21:10:12] DEBUG - DEEPSEEK_API: Making request to deepseek/deepseek-r1:free
[2025-07-09 21:10:26] DEBUG - CONVERSATION: Tool detected: web_scraping
[2025-07-09 21:10:26] DEBUG - DEEPSEEK_API: Making request to deepseek/deepseek-r1:free
[2025-07-09 21:15:23] INFO - SESSION: Nova AI Agent session started
[2025-07-09 21:15:23] INFO - SESSION: ============================================================
[2025-07-09 21:15:33] ERROR - TELEGRAM_STARTUP: Cannot close a running event loop
[2025-07-09 21:18:13] INFO - SESSION: Nova AI Agent session started
[2025-07-09 21:18:13] INFO - SESSION: ============================================================
[2025-07-09 21:18:54] DEBUG - TELEGRAM: Processing message from Hector: Nova my beautiful girl😊
[2025-07-09 21:18:56] DEBUG - CONVERSATION: Regular conversation
[2025-07-09 21:18:56] DEBUG - DEEPSEEK_API: Making request to deepseek/deepseek-r1:free
[2025-07-09 21:19:08] ERROR - TELEGRAM: log_conversation() takes 2 positional arguments but 4 were given
[2025-07-09 21:19:08] ERROR - TELEGRAM_CONTEXT: Nova my beautiful girl😊
[2025-07-09 21:19:47] DEBUG - TELEGRAM: Processing message from Hector: I was wondering if you could tell me the current time please
[2025-07-09 21:19:49] DEBUG - CONVERSATION: Tool detected: current_time
[2025-07-09 21:19:49] DEBUG - DEEPSEEK_API: Making request to deepseek/deepseek-r1:free
[2025-07-09 21:19:59] ERROR - TELEGRAM: log_conversation() takes 2 positional arguments but 4 were given
[2025-07-09 21:19:59] ERROR - TELEGRAM_CONTEXT: I was wondering if you could tell me the current time please
[2025-07-09 21:23:51] DEBUG - TELEGRAM: Processing message from Hector: Thank you😊 and yes please could you set a reminder for 10:00 pm, I need to take a coffee break
[2025-07-09 21:23:52] DEBUG - CONVERSATION: Regular conversation
[2025-07-09 21:23:52] DEBUG - DEEPSEEK_API: Making request to deepseek/deepseek-r1:free
[2025-07-09 21:24:13] ERROR - TELEGRAM: log_conversation() takes 2 positional arguments but 4 were given
[2025-07-09 21:24:13] ERROR - TELEGRAM_CONTEXT: Thank you😊 and yes please could you set a reminder for 10:00 pm, I need to take a coffee break
[2025-07-09 21:26:47] DEBUG - TELEGRAM: Processing message from Hector: Exit
[2025-07-09 21:26:49] DEBUG - CONVERSATION: Regular conversation
[2025-07-09 21:26:49] DEBUG - DEEPSEEK_API: Making request to deepseek/deepseek-r1:free
[2025-07-09 21:27:02] ERROR - TELEGRAM: log_conversation() takes 2 positional arguments but 4 were given
[2025-07-09 21:27:02] ERROR - TELEGRAM_CONTEXT: Exit
[2025-07-09 21:32:09] INFO - SESSION: Nova AI Agent session started
[2025-07-09 21:32:09] INFO - SESSION: ============================================================
[2025-07-09 21:33:07] INFO - CONVERSATION: USER: [TELEGRAM] Hector: /start
[2025-07-09 21:33:07] INFO - CONVERSATION: NOVA: 🌟 Hello Hector! I'm Nova, your AI assistant powered by DeepSeek R1.

I can help you with:
🌤️ Weather information
⏰ Time and date
🔍 Web searches and scraping
📝 Reminders and plans
🔊 System controls (if running on your computer)
🧠 Knowledge management

Just chat with me naturally! Type /help for more commands.
[2025-07-09 21:33:07] INFO - CONVERSATION: ----------------------------------------
[2025-07-09 21:33:59] DEBUG - TELEGRAM: Processing message from Hector: Hello Nova😊, How are you doing?
[2025-07-09 21:34:00] DEBUG - CONVERSATION: Regular conversation
[2025-07-09 21:34:00] DEBUG - DEEPSEEK_API: Making request to deepseek/deepseek-r1:free
[2025-07-09 21:34:13] INFO - CONVERSATION: USER: [TELEGRAM] Hector: Hello Nova😊, How are you doing?
[2025-07-09 21:34:13] INFO - CONVERSATION: NOVA: Hi there! 😊 I'm doing great, thanks for asking—always happy to be here and ready to chat or help out. How about *you*? Anything fun, exciting, or just-on-your-mind today? Let me know! 🌟
[2025-07-09 21:34:13] INFO - CONVERSATION: ----------------------------------------
[2025-07-09 21:35:20] DEBUG - TELEGRAM: Processing message from Hector: Yes there are, but before we speak about that, can you tell me what the current time is please?
[2025-07-09 21:35:23] DEBUG - CONVERSATION: Tool detected: current_time
[2025-07-09 21:35:23] DEBUG - DEEPSEEK_API: Making request to deepseek/deepseek-r1:free
[2025-07-09 21:35:40] INFO - CONVERSATION: USER: [TELEGRAM] Hector: Yes there are, but before we speak about that, can you tell me what the current time is please?
[2025-07-09 21:35:40] INFO - CONVERSATION: NOVA: Good evening, Hector! 🌙 It’s 9:35 PM on Wednesday, July 9th—perfect night for a cozy chat or a relaxed wind-down. How can I help you tonight? Whether it’s picking up where we left off or anything else on your mind, I’m here for it! 😊
[2025-07-09 21:35:40] INFO - CONVERSATION: ----------------------------------------
[2025-07-09 21:37:33] DEBUG - TELEGRAM: Processing message from Hector: Thank you, yes could you set a reminder for 10:00 PM to take a coffee break
[2025-07-09 21:37:35] ERROR - TELEGRAM: add_reminder() missing 1 required positional argument: 'when'
[2025-07-09 21:37:35] ERROR - TELEGRAM_CONTEXT: Thank you, yes could you set a reminder for 10:00 PM to take a coffee break
