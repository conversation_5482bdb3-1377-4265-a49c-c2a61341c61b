[2025-07-09 18:45:23] INFO - SESSION: Nova AI Agent session started
[2025-07-09 18:45:23] INFO - SESSION: ============================================================
[2025-07-09 18:45:38] DEBUG - API: Trying model: deepseek/deepseek-r1:free
[2025-07-09 18:45:38] DEBUG - API: Making API call with 2 messages
[2025-07-09 18:45:50] DEBUG - API: Response status: 200
[2025-07-09 18:45:50] DEBUG - API: Response data keys: ['id', 'provider', 'model', 'object', 'created', 'choices', 'usage']
[2025-07-09 18:45:50] INFO - API: Model: deepseek/deepseek-r1:free, Status: 200
[2025-07-09 18:45:50] DEBUG - API: Response preview: Hello! Yes, I can hear you. 😊 How can I assist you today?
[2025-07-09 18:50:34] INFO - SESSION: Nova AI Agent session started
[2025-07-09 18:50:34] INFO - SESSION: ============================================================
[2025-07-09 18:52:31] DEBUG - CHAT: Processing user input: Hello Nova, how are you?
[2025-07-09 18:52:31] DEBUG - CHAT: No tool detected, proceeding with regular conversation
[2025-07-09 18:52:31] DEBUG - CHAT: Skipping knowledge search for simple greeting/short input
[2025-07-09 18:52:31] ERROR - MAIN_LOOP: Expecting value: line 1 column 1 (char 0)
[2025-07-09 18:52:31] ERROR - MAIN_LOOP_CONTEXT: Hello Nova, how are you?
[2025-07-09 18:52:59] DEBUG - CHAT: Processing user input: what is quantum computing?
[2025-07-09 18:52:59] DEBUG - CHAT: No tool detected, proceeding with regular conversation
[2025-07-09 18:53:00] DEBUG - CHAT: No relevant knowledge found, proceeding normally
[2025-07-09 18:53:00] ERROR - KNOWLEDGE_SEARCH: Expecting value: line 1 column 1 (char 0)
[2025-07-09 18:53:00] ERROR - KNOWLEDGE_SEARCH_CONTEXT: what is quantum computing?
[2025-07-09 18:53:00] ERROR - MAIN_LOOP: Expecting value: line 1 column 1 (char 0)
[2025-07-09 18:53:00] ERROR - MAIN_LOOP_CONTEXT: what is quantum computing?
[2025-07-09 18:53:22] DEBUG - CHAT: Processing user input: whats your name?
[2025-07-09 18:53:22] DEBUG - CHAT: No tool detected, proceeding with regular conversation
[2025-07-09 18:53:22] DEBUG - CHAT: Skipping knowledge search for simple greeting/short input
[2025-07-09 18:53:22] ERROR - MAIN_LOOP: Expecting value: line 1 column 1 (char 0)
[2025-07-09 18:53:22] ERROR - MAIN_LOOP_CONTEXT: whats your name?
[2025-07-09 19:04:06] INFO - SESSION: Nova AI Agent session started
[2025-07-09 19:04:06] INFO - SESSION: ============================================================
[2025-07-09 19:04:18] DEBUG - CHAT: Processing user input: Hello Nova, how are you?
[2025-07-09 19:04:18] DEBUG - CHAT: No tool detected, proceeding with regular conversation
[2025-07-09 19:04:18] DEBUG - CHAT: Skipping knowledge search for simple greeting/short input
[2025-07-09 19:04:18] ERROR - MAIN_LOOP: Expecting value: line 1 column 1 (char 0)
[2025-07-09 19:04:18] ERROR - MAIN_LOOP_CONTEXT: Hello Nova, how are you?
[2025-07-09 19:05:10] DEBUG - CHAT: Processing user input: hi
[2025-07-09 19:05:10] DEBUG - CHAT: No tool detected, proceeding with regular conversation
[2025-07-09 19:05:10] DEBUG - CHAT: Skipping knowledge search for simple greeting/short input
[2025-07-09 19:05:14] ERROR - MAIN_LOOP: Expecting value: line 1 column 1 (char 0)
[2025-07-09 19:05:14] ERROR - MAIN_LOOP_CONTEXT: hi
[2025-07-09 19:22:37] INFO - SESSION: Nova AI Agent session started
[2025-07-09 19:22:37] INFO - SESSION: ============================================================
[2025-07-09 19:23:04] DEBUG - CHAT: Processing user input: Hi nova, how you?
[2025-07-09 19:23:04] DEBUG - CHAT: No tool detected, proceeding with regular conversation
[2025-07-09 19:23:04] DEBUG - CHAT: Skipping knowledge search for simple greeting/short input
[2025-07-09 19:23:04] ERROR - MAIN_LOOP: Expecting value: line 1 column 1 (char 0)
[2025-07-09 19:23:04] ERROR - MAIN_LOOP_CONTEXT: Hi nova, how you?
