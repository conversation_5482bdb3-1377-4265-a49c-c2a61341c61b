# test_custom_prompts.py
"""
Test the new custom prompt approach for DeepSeek integration
"""

from core import nova_tools

def test_simple_response():
    """Test the simple DeepSeek response function"""
    print("🧪 Testing Simple DeepSeek Response...")
    
    test_prompt = "Hello! I'm <PERSON>, your AI assistant. The user just asked 'How are you?' Please respond warmly and naturally as <PERSON>."
    
    response = nova_tools.get_simple_deepseek_response(test_prompt)
    
    if response:
        print(f"✅ Simple response successful!")
        print(f"📝 Response: {response}")
        return True
    else:
        print("❌ Simple response failed")
        return False

def test_weather_prompt():
    """Test weather tool with custom prompt"""
    print("\n🧪 Testing Weather Tool with Custom Prompt...")
    
    # Simulate weather data
    weather_data = "Temperature: 22°C, Condition: Partly cloudy, Humidity: 65%, Wind: 10 km/h, Location: London"
    
    prompt = nova_tools.create_tool_prompt(
        "What's the weather like?",
        "weather_current", 
        weather_data
    )
    
    print(f"📝 Generated prompt: {prompt[:200]}...")
    
    response = nova_tools.get_simple_deepseek_response(prompt)
    
    if response:
        print(f"✅ Weather prompt successful!")
        print(f"🌤️ Response: {response}")
        return True
    else:
        print("❌ Weather prompt failed")
        return False

def test_time_prompt():
    """Test time tool with custom prompt"""
    print("\n🧪 Testing Time Tool with Custom Prompt...")
    
    # Simulate time data
    time_data = "Current time: 2:30 PM EST, Tuesday, January 9, 2025"
    
    prompt = nova_tools.create_tool_prompt(
        "What time is it?",
        "current_time",
        time_data
    )
    
    print(f"📝 Generated prompt: {prompt[:200]}...")
    
    response = nova_tools.get_simple_deepseek_response(prompt)
    
    if response:
        print(f"✅ Time prompt successful!")
        print(f"⏰ Response: {response}")
        return True
    else:
        print("❌ Time prompt failed")
        return False

def test_search_prompt():
    """Test search tool with custom prompt"""
    print("\n🧪 Testing Search Tool with Custom Prompt...")
    
    # Simulate search data
    search_data = {
        "query": "latest AI news",
        "results": [
            {
                "title": "New AI Breakthrough in 2025",
                "snippet": "Scientists have developed a new AI model that can understand complex reasoning...",
                "source": "TechNews"
            },
            {
                "title": "AI Industry Updates",
                "snippet": "The AI industry continues to grow with new innovations in machine learning...",
                "source": "AIDaily"
            }
        ]
    }
    
    prompt = nova_tools.create_tool_prompt(
        "Search for latest AI news",
        "web_search",
        search_data
    )
    
    print(f"📝 Generated prompt: {prompt[:300]}...")
    
    response = nova_tools.get_simple_deepseek_response(prompt)
    
    if response:
        print(f"✅ Search prompt successful!")
        print(f"🔍 Response: {response}")
        return True
    else:
        print("❌ Search prompt failed")
        return False

def test_full_tool_flow():
    """Test the complete tool flow"""
    print("\n🧪 Testing Full Tool Flow...")
    
    # Test greeting detection
    tool_type, tool_data = nova_tools.get_tool_data("Hello Nova, how are you?")
    if tool_type is None:
        print("✅ Greeting correctly ignored")
    else:
        print(f"❌ Greeting incorrectly triggered: {tool_type}")
        return False
    
    # Test weather detection
    tool_type, tool_data = nova_tools.get_tool_data("What's the weather like?")
    if tool_type == "weather_current":
        print("✅ Weather correctly detected")
        
        # Test processing (with mock data since we don't want to call real weather API)
        mock_weather = "Temperature: 20°C, Sunny, London"
        response = nova_tools.process_with_deepseek(
            "What's the weather like?",
            "weather_current",
            mock_weather,
            []  # Empty memory
        )
        
        if response and len(response) > 10:
            print("✅ Weather processing successful")
            print(f"🌤️ Response: {response[:100]}...")
            return True
        else:
            print("❌ Weather processing failed")
            return False
    else:
        print(f"❌ Weather detection failed: {tool_type}")
        return False

if __name__ == "__main__":
    print("🌟 Nova AI Agent - Custom Prompts Test")
    print("=" * 60)
    
    # Check API key
    try:
        from config.api_keys import OPENROUTER_API_KEY
        if not OPENROUTER_API_KEY or OPENROUTER_API_KEY == "YOUR_OPENROUTER_API_KEY_HERE":
            print("❌ API key not configured!")
            exit(1)
    except:
        print("❌ API key file not found!")
        exit(1)
    
    tests_passed = 0
    total_tests = 5
    
    try:
        if test_simple_response():
            tests_passed += 1
        
        if test_weather_prompt():
            tests_passed += 1
            
        if test_time_prompt():
            tests_passed += 1
            
        if test_search_prompt():
            tests_passed += 1
            
        if test_full_tool_flow():
            tests_passed += 1
            
    except Exception as e:
        print(f"❌ Test error: {e}")
    
    print("\n" + "=" * 60)
    print(f"📊 Results: {tests_passed}/{total_tests} tests passed")
    
    if tests_passed >= 3:
        print("🎉 Custom prompts approach is working!")
        print("✨ Nova should now use DeepSeek conversationally with tool data")
    else:
        print("⚠️  Some tests failed. Check the configuration.")
