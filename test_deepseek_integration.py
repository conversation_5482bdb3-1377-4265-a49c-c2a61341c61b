# test_deepseek_integration.py
"""
Test script for the enhanced DeepSeek R1 integration in Nova AI Agent
This script tests various scenarios to ensure DeepSeek processes tool information conversationally
"""

import sys
import os
from core import nova_tools, nova_memory
from core.nova_logger import log_debug

def test_greeting_detection():
    """Test that simple greetings don't trigger tools"""
    print("\n🧪 Testing Greeting Detection...")
    
    test_cases = [
        "Hi Nova",
        "Hello there",
        "How are you doing today",
        "How you doing",
        "Good morning",
        "Hey, how's it going?",
        "What's up Nova"
    ]
    
    for greeting in test_cases:
        tool_type, tool_data = nova_tools.get_tool_data(greeting)
        if tool_type is None and tool_data is None:
            print(f"✅ '{greeting}' correctly identified as greeting")
        else:
            print(f"❌ '{greeting}' incorrectly triggered tool: {tool_type}")
    
    return True

def test_tool_detection():
    """Test that tool requests are properly detected"""
    print("\n🧪 Testing Tool Detection...")
    
    test_cases = [
        ("What's the weather like?", "weather_current"),
        ("What time is it?", "current_time"),
        ("What's today's date?", "current_date"),
        ("Search for AI news", "web_search"),
        ("Get me the latest news", "news_search"),
        ("Weather forecast for tomorrow", "weather_forecast")
    ]
    
    for query, expected_type in test_cases:
        tool_type, tool_data = nova_tools.get_tool_data(query)
        if tool_type == expected_type:
            print(f"✅ '{query}' correctly detected as {tool_type}")
        else:
            print(f"❌ '{query}' expected {expected_type}, got {tool_type}")
    
    return True

def test_deepseek_processing():
    """Test that DeepSeek processes tool data conversationally"""
    print("\n🧪 Testing DeepSeek Processing...")
    
    # Mock tool data for testing
    test_cases = [
        {
            "user_input": "What time is it?",
            "tool_type": "current_time",
            "tool_data": "Current time: 2:30 PM EST, Tuesday, January 9, 2025"
        },
        {
            "user_input": "What's the weather?",
            "tool_type": "weather_current", 
            "tool_data": "Temperature: 22°C, Condition: Partly cloudy, Humidity: 65%, Wind: 10 km/h"
        }
    ]
    
    for test_case in test_cases:
        try:
            # Load memory
            memory = nova_memory.load_memory()
            
            # Process through DeepSeek
            response = nova_tools.process_with_deepseek(
                test_case["user_input"],
                test_case["tool_type"], 
                test_case["tool_data"],
                memory
            )
            
            if response and len(response) > 10:
                print(f"✅ DeepSeek processed '{test_case['user_input']}' successfully")
                print(f"   Response preview: {response[:100]}...")
            else:
                print(f"❌ DeepSeek failed to process '{test_case['user_input']}'")
                
        except Exception as e:
            print(f"❌ Error processing '{test_case['user_input']}': {e}")
    
    return True

def test_api_connection():
    """Test API connection to DeepSeek R1"""
    print("\n🧪 Testing API Connection...")
    
    try:
        # Simple test message
        test_messages = [
            {"role": "system", "content": "You are Nova, a helpful AI assistant."},
            {"role": "user", "content": "Hello, can you hear me?"}
        ]
        
        response = nova_tools.get_chat_response(test_messages)
        
        if response and len(response) > 5:
            print("✅ API connection successful")
            print(f"   Response: {response[:100]}...")
            return True
        else:
            print("❌ API connection failed - empty response")
            return False
            
    except Exception as e:
        print(f"❌ API connection error: {e}")
        return False

def run_interactive_test():
    """Run an interactive test session"""
    print("\n🎮 Interactive Test Mode")
    print("Type messages to test the enhanced DeepSeek integration")
    print("Type 'quit' to exit")
    print("-" * 50)
    
    while True:
        try:
            user_input = input("\nYou: ").strip()
            
            if user_input.lower() in ['quit', 'exit', 'q']:
                break
                
            if not user_input:
                continue
            
            # Test the full flow
            tool_type, tool_data = nova_tools.get_tool_data(user_input)
            
            if tool_type and tool_data:
                print(f"🔧 Tool detected: {tool_type}")
                
                # Process through DeepSeek
                memory = nova_memory.load_memory()
                response = nova_tools.process_with_deepseek(user_input, tool_type, tool_data, memory)
                print(f"Nova: {response}")
                
                # Save to memory
                nova_memory.save_memory(user_input, response)
            else:
                print("💬 No tool detected - regular conversation")
                
                # Regular conversation
                memory = nova_memory.load_memory()
                memory.append({"role": "user", "content": user_input})
                response = nova_tools.get_chat_response(memory)
                print(f"Nova: {response}")
                
                # Save to memory
                nova_memory.save_memory(user_input, response)
                
        except KeyboardInterrupt:
            break
        except Exception as e:
            print(f"❌ Error: {e}")
    
    print("\n👋 Interactive test ended")

def main():
    """Main test function"""
    print("🌟 Nova AI Agent - DeepSeek R1 Integration Test")
    print("=" * 60)
    
    # Check if API key is configured
    try:
        from config.api_keys import OPENROUTER_API_KEY
        if OPENROUTER_API_KEY == "YOUR_OPENROUTER_API_KEY_HERE":
            print("❌ OpenRouter API key not configured!")
            print("Please update config/api_keys.py with your API key")
            return
    except ImportError:
        print("❌ API keys file not found!")
        return
    
    # Run tests
    tests_passed = 0
    total_tests = 4
    
    try:
        if test_greeting_detection():
            tests_passed += 1
        
        if test_tool_detection():
            tests_passed += 1
            
        if test_api_connection():
            tests_passed += 1
            
        if test_deepseek_processing():
            tests_passed += 1
            
    except Exception as e:
        print(f"❌ Test suite error: {e}")
    
    # Results
    print("\n" + "=" * 60)
    print(f"📊 Test Results: {tests_passed}/{total_tests} tests passed")
    
    if tests_passed == total_tests:
        print("🎉 All tests passed! DeepSeek integration is working correctly.")
        
        # Offer interactive test
        response = input("\nWould you like to run interactive test? (y/n): ").lower()
        if response in ['y', 'yes']:
            run_interactive_test()
    else:
        print("⚠️  Some tests failed. Please check the configuration and try again.")

if __name__ == "__main__":
    main()
