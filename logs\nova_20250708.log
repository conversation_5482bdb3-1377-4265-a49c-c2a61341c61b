
[2025-07-08 23:08:24] INFO - SESSION: Nova AI Agent session started
[2025-07-08 23:08:24] INFO - SESSION: ============================================================
[2025-07-08 23:08:24] DEBUG - API: Trying model: deepseek/deepseek-r1:free
[2025-07-08 23:08:24] DEBUG - API: Making API call with 1 messages
[2025-07-08 23:08:33] DEBUG - API: Response status: 200
[2025-07-08 23:08:33] DEBUG - API: Response data keys: ['id', 'provider', 'model', 'object', 'created', 'choices', 'usage']
[2025-07-08 23:08:33] INFO - API: Model: deepseek/deepseek-r1:free, Status: 200
[2025-07-08 23:08:33] DEBUG - API: Response preview: Hi there! How can I assist you today? 😊
[2025-07-08 23:10:26] INFO - SESSION: Nova AI Agent session started
[2025-07-08 23:10:27] INFO - SESSION: ============================================================
[2025-07-08 23:10:55] DEBUG - CHAT: Processing user input: Hi Nova, how you doing today?
[2025-07-08 23:10:55] DEBUG - CHAT: No tool detected, proceeding with regular conversation
[2025-07-08 23:10:55] DEBUG - CHAT: Skipping knowledge search for simple greeting/short input
[2025-07-08 23:10:55] ERROR - MAIN_LOOP: Expecting value: line 1 column 1 (char 0)
[2025-07-08 23:10:55] ERROR - MAIN_LOOP_CONTEXT: Hi Nova, how you doing today?
