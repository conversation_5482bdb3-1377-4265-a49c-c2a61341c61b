# nova_memory.py
import json
import os
from config import nova_config as conf

def load_memory():
    system_prompt = (
        f"You are {conf.NOVA_NAME}, a warm {conf.USER_NAME}'s female AI companion. "
        "You're empathetic, conversational",
        "You remember previous chats "
        f"Current context: {get_current_context()}"
    )
    
    if not os.path.exists(conf.MEMORY_FILE):
        return [{"role": "system", "content": system_prompt}]
    
    with open(conf.MEMORY_FILE, 'r') as f:
        memory = json.load(f)
        # Prepend system prompt to context
        if memory and memory[0]["role"] != "system":
            memory.insert(0, {"role": "system", "content": system_prompt})
        return memory

def save_memory(user_input, nova_response):
    """Save conversation to memory with enhanced context tracking"""
    try:
        memory = load_memory()

        # Add conversation turn
        memory.append({"role": "user", "content": user_input})
        memory.append({"role": "assistant", "content": nova_response})

        # Keep system message + last conversations
        if len(memory) > 1 + conf.MAX_HISTORY * 2:  # accounting for pairs
            memory = memory[:1] + memory[-(conf.MAX_HISTORY * 2):]

        # Save to file with error handling
        with open(conf.MEMORY_FILE, 'w', encoding='utf-8') as f:
            json.dump(memory, f, indent=2, ensure_ascii=False)

        # Also save important information to knowledge base
        try:
            from core.nova_knowledge import add_knowledge
            # Save user preferences or important facts mentioned
            if any(keyword in user_input.lower() for keyword in ["my name is", "i am", "i like", "i prefer", "remember that"]):
                add_knowledge("User Preference", user_input, "conversation", importance=3)
        except Exception:
            pass  # Don't fail if knowledge base isn't available

    except Exception as e:
        print(f"Error saving memory: {e}")
        # Fallback: try to save without encoding
        try:
            with open(conf.MEMORY_FILE, 'w') as f:
                json.dump(memory, f, indent=2)
        except Exception:
            pass  # Silent fail to prevent crashes

def clear_memory():
    """Reset conversation history except system prompt"""
    with open(conf.MEMORY_FILE, 'w') as f:
        json.dump(load_memory()[:1], f)  # Keep only system message

def get_current_context():
    """Provide current context for the system prompt"""
    from utils.helper_functions import get_formatted_time
    return f"It's currently {get_formatted_time()}. Engage naturally and respond conversationally."
