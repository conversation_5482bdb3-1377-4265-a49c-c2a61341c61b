# nova_tools_clean.py - Clean version with only DeepSeek custom prompts
from duckduckgo_search import DDGS
import requests
from config import nova_config as conf
from config.api_keys import OPENROUTER_API_KEY

# Import Nova's capabilities
from core.nova_weather import get_weather, get_forecast
from core.nova_datetime import get_current_time, get_date_info, time_until

def perform_web_search(query, max_results=conf.MAX_SEARCH_RESULTS):
    """Perform web search and return cleaned results"""
    try:
        with DDGS() as ddgs:
            results = []
            dedupe = set()
            
            for result in ddgs.text(query, max_results=max_results*3):
                # Deduplicate by domain
                domain = result.get('hostname', result.get('href','').split('//')[-1].split('/')[0])
                if domain in dedupe:
                    continue
                dedupe.add(domain)
                
                results.append({
                    'title': result.get('title', 'No title'),
                    'snippet': result.get('body', 'No description'),
                    'source': domain,
                    'url': result.get('href', '')
                })
                
                if len(results) >= max_results:
                    break
            
            return results
    except Exception as e:
        print(f"Search error: {e}")
        return []

def get_simple_deepseek_response(prompt):
    """Get a simple response from DeepSeek using a single prompt - no complex JSON parsing"""
    from core.nova_logger import log_error, log_debug
    
    headers = {
        "Authorization": f"Bearer {OPENROUTER_API_KEY}",
        "HTTP-Referer": conf.WEBSITE_URL,
        "X-Title": conf.APP_NAME,
        "Content-Type": "application/json"
    }
    
    payload = {
        "model": conf.DEEPSEEK_MODEL,
        "messages": [{"role": "user", "content": prompt}],
        "temperature": 0.8,
        "max_tokens": 1500,
        "stream": False
    }
    
    log_debug("SIMPLE_API", f"Making simple request to {conf.DEEPSEEK_MODEL}")
    
    try:
        response = requests.post(conf.OPENROUTER_URL, headers=headers, json=payload, timeout=30)
        
        if response.status_code == 200:
            # Try JSON parsing first
            try:
                response_data = response.json()
                if 'choices' in response_data and response_data['choices']:
                    content = response_data['choices'][0].get('message', {}).get('content', '')
                    if content:
                        return content.strip()
            except:
                pass
            
            # If JSON fails, try to extract any readable text from the response
            raw_text = response.text.strip()
            if raw_text and len(raw_text) > 10:
                # Look for any text that looks like a response
                if not raw_text.startswith('{'):
                    return raw_text
                else:
                    # Try to find readable text in the response
                    lines = raw_text.split('\n')
                    for line in lines:
                        line = line.strip()
                        if line and not line.startswith('{') and not line.startswith('"') and len(line) > 20:
                            return line
        
        log_error("SIMPLE_API", f"Failed to get response: {response.status_code}")
        return None
        
    except Exception as e:
        log_error("SIMPLE_API", f"Error: {e}")
        return None

def get_tool_data(user_input):
    """Execute tools and return raw data instead of formatted responses"""
    user_lower = user_input.lower().strip()

    # Enhanced greeting detection
    greeting_patterns = [
        "hi", "hello", "hey", "how are you", "how you doing", "how are you doing",
        "good morning", "good afternoon", "good evening", "good night",
        "what's up", "sup", "yo", "how's it going", "how you been",
        "how are things", "how's everything", "how's your day",
        "how you doing today", "how are you today", "how's it going today"
    ]

    # Check if this is just a simple greeting
    if any(greeting in user_lower for greeting in greeting_patterns) and len(user_input.strip()) < 50:
        request_keywords = ["weather", "time", "remind", "search", "news", "volume", "open", "close"]
        if not any(keyword in user_lower for keyword in request_keywords):
            return None, None  # No tool data

    # Weather requests - return raw data
    if any(phrase in user_lower for phrase in ["weather", "temperature", "forecast", "rain", "sunny", "cloudy"]):
        if "forecast" in user_lower or "tomorrow" in user_lower or "week" in user_lower:
            city = extract_city_from_input(user_input)
            forecast_data = get_forecast(city)
            return "weather_forecast", forecast_data
        else:
            city = extract_city_from_input(user_input)
            weather_data = get_weather(city)
            return "weather_current", weather_data

    # Time and date requests
    elif any(phrase in user_lower for phrase in ["what time", "current time", "time is it", "clock"]):
        time_data = get_current_time()
        return "current_time", time_data
    elif any(phrase in user_lower for phrase in ["what date", "today's date", "date today", "calendar", "what day"]) and not any(phrase in user_lower for phrase in ["how", "doing", "are you"]):
        date_data = get_date_info()
        return "current_date", date_data

    # Web search requests
    elif any(phrase in user_lower for phrase in ["search", "look up", "find", "google"]):
        search_query = user_input
        for phrase in ["search for", "look up", "find", "google"]:
            if phrase in user_lower:
                search_query = user_input.lower().split(phrase)[-1].strip()
                break
        search_results = perform_web_search(search_query)
        return "web_search", {"query": search_query, "results": search_results}

    # News requests
    elif any(phrase in user_lower for phrase in ["news", "latest news", "current events", "headlines", "breaking news"]):
        if any(word in user_lower for word in ["get", "show", "tell", "what", "find", "search"]) or user_lower.startswith("news"):
            news_query = user_input
            for phrase in ["news about", "latest news on", "news on", "headlines about"]:
                if phrase in user_lower:
                    news_query = user_input.lower().split(phrase)[-1].strip()
                    break
            else:
                if any(word in user_lower for word in ["latest", "current", "breaking"]) and "news" in user_lower:
                    news_query = "latest news today"
                else:
                    news_query = user_input
            search_results = perform_web_search(news_query)
            return "news_search", {"query": news_query, "results": search_results}

    return None, None  # No tool data

def extract_city_from_input(user_input):
    """Extract city name from weather request"""
    words = user_input.split()
    prepositions = ["in", "for", "at"]

    for i, word in enumerate(words):
        if word.lower() in prepositions and i + 1 < len(words):
            return " ".join(words[i+1:]).strip("?.,!")

    return None

def create_tool_prompt(user_input, tool_type, tool_data):
    """Create a custom prompt that includes tool data for DeepSeek to process conversationally"""
    
    if tool_type == "weather_current":
        return f"""The user asked: "{user_input}"

I just checked the current weather and here's what I found: {tool_data}

Please respond naturally as Nova, a friendly AI assistant, incorporating this weather information into a conversational response. Be warm and helpful."""

    elif tool_type == "weather_forecast":
        return f"""The user asked: "{user_input}"

I just checked the weather forecast and here's what I found: {tool_data}

Please respond naturally as Nova, a friendly AI assistant, incorporating this forecast information into a conversational response. Be warm and helpful."""

    elif tool_type == "current_time":
        return f"""The user asked: "{user_input}"

I just checked the current time and here's what I found: {tool_data}

Please respond naturally as Nova, a friendly AI assistant, incorporating this time information into a conversational response. Be warm and helpful."""

    elif tool_type == "current_date":
        return f"""The user asked: "{user_input}"

I just checked the current date and here's what I found: {tool_data}

Please respond naturally as Nova, a friendly AI assistant, incorporating this date information into a conversational response. Be warm and helpful."""

    elif tool_type == "web_search":
        results_text = ""
        for i, result in enumerate(tool_data["results"][:4], 1):
            results_text += f"{i}. {result['title']}\n   {result['snippet'][:150]}...\n   Source: {result['source']}\n\n"
        
        return f"""The user asked: "{user_input}"

I just searched the web for "{tool_data['query']}" and found these results:

{results_text}

Please respond naturally as Nova, a friendly AI assistant, summarizing this information in a conversational way. Be warm, helpful, and mention the sources naturally."""

    elif tool_type == "news_search":
        results_text = ""
        for i, result in enumerate(tool_data["results"][:4], 1):
            results_text += f"{i}. {result['title']}\n   {result['snippet'][:150]}...\n   Source: {result['source']}\n\n"
        
        return f"""The user asked: "{user_input}"

I just searched for the latest news about "{tool_data['query']}" and found these results:

{results_text}

Please respond naturally as Nova, a friendly AI assistant, sharing this news in a conversational way. Be warm, helpful, and mention the sources naturally."""

    else:
        return f"""The user asked: "{user_input}"

I used a tool to get this information: {tool_data}

Please respond naturally as Nova, a friendly AI assistant, incorporating this information into a conversational response. Be warm and helpful."""

def process_with_deepseek(user_input, tool_type, tool_data, memory):
    """Process tool data through DeepSeek using custom prompts instead of JSON"""
    from core.nova_logger import log_debug
    
    # Create a custom prompt that includes the tool data
    tool_prompt = create_tool_prompt(user_input, tool_type, tool_data)
    
    log_debug("DEEPSEEK_TOOL", f"Processing {tool_type} with custom prompt")
    log_debug("DEEPSEEK_PROMPT", f"Custom prompt: {tool_prompt[:200]}...")
    
    # Use the simple API method to avoid JSON parsing issues
    response = get_simple_deepseek_response(tool_prompt)
    
    if response:
        return response
    else:
        # Fallback response if API fails
        log_debug("DEEPSEEK_FALLBACK", "Using fallback response")
        return f"I got some information about your request, but I'm having trouble processing it right now. Could you try asking again? 🤔"

# Deprecated functions for compatibility
def get_chat_response(messages):
    """DEPRECATED - Use get_simple_deepseek_response instead"""
    from core.nova_logger import log_debug
    
    log_debug("API", "WARNING: get_chat_response is deprecated, using simple method")
    
    if not messages:
        return "I'm having trouble processing your request. Please try again."
    
    # Get the last user message
    user_message = ""
    for msg in reversed(messages):
        if msg['role'] == 'user':
            user_message = msg['content']
            break
    
    if not user_message:
        return "I'm having trouble processing your request. Please try again."
    
    # Create simple prompt
    simple_prompt = f"You are Nova, a warm and friendly AI assistant. The user said: '{user_message}'. Please respond naturally and helpfully."
    
    response = get_simple_deepseek_response(simple_prompt)
    if response:
        return response
    else:
        return "I'm having some trouble right now. Could you try asking again? 🤔"

def process_tool_request(user_input):
    """DEPRECATED - Use get_tool_data instead"""
    return None
