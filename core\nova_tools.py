# nova_tools.py - Tools only, no conversation handling
from duckduckgo_search import DDGS

# Import Nova's tool capabilities
from core.nova_weather import get_weather, get_forecast
from core.nova_datetime import get_current_time, get_date_info, time_until
from core.nova_reminders import add_reminder, list_reminders, remove_reminder, add_plan, list_plans
from core.nova_windows import (set_volume, get_volume, mute_volume, unmute_volume,
                              open_application, close_application, list_running_apps,
                              take_screenshot, lock_computer, shutdown_computer, restart_computer)
from core.nova_knowledge import add_knowledge, search_knowledge, get_recent_knowledge

def perform_web_search(query, max_results=4):
    """Perform web search and return cleaned results"""
    try:
        with DDGS() as ddgs:
            results = []
            dedupe = set()

            for result in ddgs.text(query, max_results=max_results*3):
                # Deduplicate by domain
                domain = result.get('hostname', result.get('href','').split('//')[-1].split('/')[0])
                if domain in dedupe:
                    continue
                dedupe.add(domain)

                results.append({
                    'title': result.get('title', 'No title'),
                    'snippet': result.get('body', 'No description'),
                    'source': domain,
                    'url': result.get('href', '')
                })

                if len(results) >= max_results:
                    break

            return results
    except Exception as e:
        print(f"Search error: {e}")
        return []

def extract_city_from_input(user_input):
    """Extract city name from weather request"""
    words = user_input.split()
    prepositions = ["in", "for", "at"]

    for i, word in enumerate(words):
        if word.lower() in prepositions and i + 1 < len(words):
            return " ".join(words[i+1:]).strip("?.,!")

    return None

# All conversation handling removed - only tool functions remain

# Tool execution functions
def execute_weather_tool(user_input):
    """Execute weather tool and return data"""
    user_lower = user_input.lower()
    city = extract_city_from_input(user_input)

    if "forecast" in user_lower or "tomorrow" in user_lower or "week" in user_lower:
        return get_forecast(city)
    else:
        return get_weather(city)

def execute_time_tool():
    """Execute time tool and return data"""
    return get_current_time()

def execute_date_tool():
    """Execute date tool and return data"""
    return get_date_info()

def execute_search_tool(query):
    """Execute web search tool and return data"""
    return perform_web_search(query)

def execute_reminder_tool(action, data=None):
    """Execute reminder tool and return data"""
    if action == "add":
        return add_reminder(data)
    elif action == "list":
        return list_reminders()
    elif action == "remove":
        return remove_reminder(data)
    else:
        return "Invalid reminder action"

def execute_plan_tool(action, data=None):
    """Execute plan tool and return data"""
    if action == "add":
        return add_plan(data)
    elif action == "list":
        return list_plans()
    else:
        return "Invalid plan action"

def execute_volume_tool(action, level=None):
    """Execute volume tool and return data"""
    if action == "set" and level is not None:
        return set_volume(level)
    elif action == "get":
        return get_volume()
    elif action == "mute":
        return mute_volume()
    elif action == "unmute":
        return unmute_volume()
    else:
        return "Invalid volume action"

def execute_app_tool(action, app_name):
    """Execute application tool and return data"""
    if action == "open":
        return open_application(app_name)
    elif action == "close":
        return close_application(app_name)
    elif action == "list":
        return list_running_apps()
    else:
        return "Invalid app action"

def execute_system_tool(action):
    """Execute system tool and return data"""
    if action == "screenshot":
        return take_screenshot()
    elif action == "lock":
        return lock_computer()
    elif action == "shutdown":
        return shutdown_computer()
    elif action == "restart":
        return restart_computer()
    else:
        return "Invalid system action"

def execute_knowledge_tool(action, data=None):
    """Execute knowledge tool and return data"""
    if action == "add":
        return add_knowledge("user_note", data)
    elif action == "search":
        return search_knowledge(data)
    elif action == "recent":
        return get_recent_knowledge()
    else:
        return "Invalid knowledge action"
