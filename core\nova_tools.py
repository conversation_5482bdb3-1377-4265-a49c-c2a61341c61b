# nova_tools.py - Tools + DeepSeek R1 Conversation
from duckduckgo_search import DDGS
import requests
from config import nova_config as conf
from config.api_keys import OPENROUTER_API_KEY

# Import Nova's tool capabilities
from core.nova_weather import get_weather, get_forecast
from core.nova_datetime import get_current_time, get_date_info, time_until
from core.nova_reminders import add_reminder, list_reminders, remove_reminder, add_plan, list_plans
from core.nova_windows import (set_volume, get_volume, mute_volume, unmute_volume,
                              open_application, close_application, list_running_apps,
                              take_screenshot, lock_computer, shutdown_computer, restart_computer)
from core.nova_knowledge import add_knowledge, search_knowledge, get_recent_knowledge, scrape_and_store

def perform_web_search(query, max_results=4):
    """Perform web search and return cleaned results"""
    try:
        with DDGS() as ddgs:
            results = []
            dedupe = set()

            for result in ddgs.text(query, max_results=max_results*3):
                # Deduplicate by domain
                domain = result.get('hostname', result.get('href','').split('//')[-1].split('/')[0])
                if domain in dedupe:
                    continue
                dedupe.add(domain)

                results.append({
                    'title': result.get('title', 'No title'),
                    'snippet': result.get('body', 'No description'),
                    'source': domain,
                    'url': result.get('href', '')
                })

                if len(results) >= max_results:
                    break

            return results
    except Exception as e:
        print(f"Search error: {e}")
        return []

def extract_city_from_input(user_input):
    """Extract city name from weather request"""
    words = user_input.split()
    prepositions = ["in", "for", "at"]

    for i, word in enumerate(words):
        if word.lower() in prepositions and i + 1 < len(words):
            return " ".join(words[i+1:]).strip("?.,!")

    return None

# DeepSeek R1 Conversation Functions
def get_deepseek_response(prompt):
    """Get response from DeepSeek R1 using custom prompts"""
    from core.nova_logger import log_error, log_debug

    headers = {
        "Authorization": f"Bearer {OPENROUTER_API_KEY}",
        "HTTP-Referer": conf.WEBSITE_URL,
        "X-Title": conf.APP_NAME,
        "Content-Type": "application/json"
    }

    payload = {
        "model": conf.DEEPSEEK_MODEL,
        "messages": [{"role": "user", "content": prompt}],
        "temperature": 0.8,
        "max_tokens": 1500,
        "stream": False
    }

    log_debug("DEEPSEEK_API", f"Making request to {conf.DEEPSEEK_MODEL}")

    try:
        response = requests.post(conf.OPENROUTER_URL, headers=headers, json=payload, timeout=30)

        if response.status_code == 200:
            # Try JSON parsing first
            try:
                response_data = response.json()
                if 'choices' in response_data and response_data['choices']:
                    content = response_data['choices'][0].get('message', {}).get('content', '')
                    if content:
                        return content.strip()
            except:
                pass

            # If JSON fails, try to extract any readable text from the response
            raw_text = response.text.strip()
            if raw_text and len(raw_text) > 10:
                # Look for any text that looks like a response
                if not raw_text.startswith('{'):
                    return raw_text
                else:
                    # Try to find readable text in the response
                    lines = raw_text.split('\n')
                    for line in lines:
                        line = line.strip()
                        if line and not line.startswith('{') and not line.startswith('"') and len(line) > 20:
                            return line

        log_error("DEEPSEEK_API", f"Failed to get response: {response.status_code}")
        return None

    except Exception as e:
        log_error("DEEPSEEK_API", f"Error: {e}")
        return None

def detect_tool_request(user_input):
    """Detect if user input is a tool request and return tool type and data"""
    user_lower = user_input.lower().strip()

    # Enhanced greeting detection - don't trigger tools for greetings
    greeting_patterns = [
        "hi", "hello", "hey", "how are you", "how you doing", "how are you doing",
        "good morning", "good afternoon", "good evening", "good night",
        "what's up", "sup", "yo", "how's it going", "how you been",
        "how are things", "how's everything", "how's your day",
        "how you doing today", "how are you today", "how's it going today"
    ]

    # Check if this is just a simple greeting
    if any(greeting in user_lower for greeting in greeting_patterns) and len(user_input.strip()) < 50:
        request_keywords = ["weather", "time", "remind", "search", "news", "volume", "open", "close"]
        if not any(keyword in user_lower for keyword in request_keywords):
            return None, None  # No tool needed

    # Weather requests
    if any(phrase in user_lower for phrase in ["weather", "temperature", "forecast", "rain", "sunny", "cloudy"]):
        if "forecast" in user_lower or "tomorrow" in user_lower or "week" in user_lower:
            city = extract_city_from_input(user_input)
            forecast_data = execute_weather_tool(user_input)
            return "weather_forecast", forecast_data
        else:
            weather_data = execute_weather_tool(user_input)
            return "weather_current", weather_data

    # Time and date requests
    elif any(phrase in user_lower for phrase in ["what time", "current time", "time is it", "clock"]):
        time_data = execute_time_tool()
        return "current_time", time_data
    elif any(phrase in user_lower for phrase in ["what date", "today's date", "date today", "calendar", "what day"]) and not any(phrase in user_lower for phrase in ["how", "doing", "are you"]):
        date_data = execute_date_tool()
        return "current_date", date_data

    # Web search requests
    elif any(phrase in user_lower for phrase in ["search", "look up", "find", "google"]):
        search_query = user_input
        for phrase in ["search for", "look up", "find", "google"]:
            if phrase in user_lower:
                search_query = user_input.lower().split(phrase)[-1].strip()
                break
        search_results = execute_search_tool(search_query)
        return "web_search", {"query": search_query, "results": search_results}

    # News requests
    elif any(phrase in user_lower for phrase in ["news", "latest news", "current events", "headlines", "breaking news"]):
        if any(word in user_lower for word in ["get", "show", "tell", "what", "find", "search"]) or user_lower.startswith("news"):
            news_query = user_input
            for phrase in ["news about", "latest news on", "news on", "headlines about"]:
                if phrase in user_lower:
                    news_query = user_input.lower().split(phrase)[-1].strip()
                    break
            else:
                if any(word in user_lower for word in ["latest", "current", "breaking"]) and "news" in user_lower:
                    news_query = "latest news today"
                else:
                    news_query = user_input
            search_results = execute_search_tool(news_query)
            return "news_search", {"query": news_query, "results": search_results}

    # Web scraping requests
    elif any(phrase in user_lower for phrase in ["scrape", "extract", "get content from"]) or "http" in user_input:
        # Extract URL from input
        words = user_input.split()
        url = None
        for word in words:
            if word.startswith("http"):
                url = word
                break

        if url:
            scraped_data = execute_scraping_tool(url)
            return "web_scraping", {"url": url, "content": scraped_data}
        else:
            return "web_scraping", {"error": "No valid URL found in request"}

    return None, None  # No tool needed

def create_tool_prompt(user_input, tool_type, tool_data):
    """Create a custom prompt that includes tool data for DeepSeek to process conversationally"""

    if tool_type == "weather_current":
        return f"""The user asked: "{user_input}"

I just checked the current weather and here's what I found: {tool_data}

Please respond naturally as Nova, a friendly AI assistant, incorporating this weather information into a conversational response. Be warm and helpful."""

    elif tool_type == "weather_forecast":
        return f"""The user asked: "{user_input}"

I just checked the weather forecast and here's what I found: {tool_data}

Please respond naturally as Nova, a friendly AI assistant, incorporating this forecast information into a conversational response. Be warm and helpful."""

    elif tool_type == "current_time":
        return f"""The user asked: "{user_input}"

I just checked the current time and here's what I found: {tool_data}

Please respond naturally as Nova, a friendly AI assistant, incorporating this time information into a conversational response. Be warm and helpful."""

    elif tool_type == "current_date":
        return f"""The user asked: "{user_input}"

I just checked the current date and here's what I found: {tool_data}

Please respond naturally as Nova, a friendly AI assistant, incorporating this date information into a conversational response. Be warm and helpful."""

    elif tool_type == "web_search":
        results_text = ""
        for i, result in enumerate(tool_data["results"][:4], 1):
            results_text += f"{i}. {result['title']}\n   {result['snippet'][:150]}...\n   Source: {result['source']}\n\n"

        return f"""The user asked: "{user_input}"

I just searched the web for "{tool_data['query']}" and found these results:

{results_text}

Please respond naturally as Nova, a friendly AI assistant, summarizing this information in a conversational way. Be warm, helpful, and mention the sources naturally."""

    elif tool_type == "news_search":
        results_text = ""
        for i, result in enumerate(tool_data["results"][:4], 1):
            results_text += f"{i}. {result['title']}\n   {result['snippet'][:150]}...\n   Source: {result['source']}\n\n"

        return f"""The user asked: "{user_input}"

I just searched for the latest news about "{tool_data['query']}" and found these results:

{results_text}

Please respond naturally as Nova, a friendly AI assistant, sharing this news in a conversational way. Be warm, helpful, and mention the sources naturally."""

    elif tool_type == "web_scraping":
        if "error" in tool_data:
            return f"""The user asked: "{user_input}"

I tried to scrape a webpage but encountered an issue: {tool_data['error']}

Please respond naturally as Nova, a friendly AI assistant, explaining the issue and offering to help in another way. Be warm and helpful."""
        else:
            content_preview = str(tool_data['content'])[:500] + "..." if len(str(tool_data['content'])) > 500 else str(tool_data['content'])
            return f"""The user asked: "{user_input}"

I just scraped the webpage at {tool_data['url']} and extracted this content:

{content_preview}

Please respond naturally as Nova, a friendly AI assistant, summarizing the scraped content in a conversational way. Be warm, helpful, and mention what you found."""

    else:
        return f"""The user asked: "{user_input}"

I used a tool to get this information: {tool_data}

Please respond naturally as Nova, a friendly AI assistant, incorporating this information into a conversational response. Be warm and helpful."""

def process_user_input(user_input):
    """Process user input - check for tools first, then use DeepSeek for conversation"""
    from core.nova_logger import log_debug

    # Check if this is a tool request
    tool_type, tool_data = detect_tool_request(user_input)

    if tool_type and tool_data:
        # This is a tool request - create custom prompt with tool data
        log_debug("CONVERSATION", f"Tool detected: {tool_type}")
        tool_prompt = create_tool_prompt(user_input, tool_type, tool_data)
        response = get_deepseek_response(tool_prompt)

        if response:
            return response
        else:
            return f"I got some information about your request, but I'm having trouble processing it right now. Could you try asking again? 🤔"

    else:
        # Regular conversation - send to DeepSeek
        log_debug("CONVERSATION", "Regular conversation")
        conversation_prompt = f"""You are Nova, a warm and friendly AI assistant. The user said: "{user_input}"

Please respond naturally and conversationally. Be helpful, warm, and engaging."""

        response = get_deepseek_response(conversation_prompt)

        if response:
            return response
        else:
            return "I'm having some trouble right now. Could you try asking again? 🤔"

# Tool execution functions
def execute_weather_tool(user_input):
    """Execute weather tool and return data"""
    user_lower = user_input.lower()
    city = extract_city_from_input(user_input)

    if "forecast" in user_lower or "tomorrow" in user_lower or "week" in user_lower:
        return get_forecast(city)
    else:
        return get_weather(city)

def execute_time_tool():
    """Execute time tool and return data"""
    return get_current_time()

def execute_date_tool():
    """Execute date tool and return data"""
    return get_date_info()

def execute_search_tool(query):
    """Execute web search tool and return data"""
    return perform_web_search(query)

def execute_reminder_tool(action, data=None):
    """Execute reminder tool and return data"""
    if action == "add":
        return add_reminder(data)
    elif action == "list":
        return list_reminders()
    elif action == "remove":
        return remove_reminder(data)
    else:
        return "Invalid reminder action"

def execute_plan_tool(action, data=None):
    """Execute plan tool and return data"""
    if action == "add":
        return add_plan(data)
    elif action == "list":
        return list_plans()
    else:
        return "Invalid plan action"

def execute_volume_tool(action, level=None):
    """Execute volume tool and return data"""
    if action == "set" and level is not None:
        return set_volume(level)
    elif action == "get":
        return get_volume()
    elif action == "mute":
        return mute_volume()
    elif action == "unmute":
        return unmute_volume()
    else:
        return "Invalid volume action"

def execute_app_tool(action, app_name):
    """Execute application tool and return data"""
    if action == "open":
        return open_application(app_name)
    elif action == "close":
        return close_application(app_name)
    elif action == "list":
        return list_running_apps()
    else:
        return "Invalid app action"

def execute_system_tool(action):
    """Execute system tool and return data"""
    if action == "screenshot":
        return take_screenshot()
    elif action == "lock":
        return lock_computer()
    elif action == "shutdown":
        return shutdown_computer()
    elif action == "restart":
        return restart_computer()
    else:
        return "Invalid system action"

def execute_knowledge_tool(action, data=None):
    """Execute knowledge tool and return data"""
    if action == "add":
        return add_knowledge("user_note", data)
    elif action == "search":
        return search_knowledge(data)
    elif action == "recent":
        return get_recent_knowledge()
    else:
        return "Invalid knowledge action"

def execute_scraping_tool(url):
    """Execute web scraping tool and return data"""
    try:
        result = scrape_and_store(url)
        return result
    except Exception as e:
        return f"Error scraping {url}: {e}"
