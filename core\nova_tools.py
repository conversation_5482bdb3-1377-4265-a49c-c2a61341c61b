# nova_tools.py
from duckduckgo_search import DDGS
import requests
import json
from utils.helper_functions import truncate_string, format_source
from config import nova_config as conf
from config.api_keys import OPENROUTER_API_KEY

# Import all Nova's new capabilities
from core.nova_weather import get_weather, get_forecast
from core.nova_datetime import get_current_time, get_date_info, time_until, get_timezone_info
from core.nova_reminders import add_reminder, list_reminders, remove_reminder, add_plan, list_plans
from core.nova_windows import (set_volume, get_volume, mute_volume, unmute_volume,
                              open_application, close_application, list_running_apps,
                              take_screenshot, lock_computer, shutdown_computer, restart_computer)
from core.nova_knowledge import add_knowledge, search_knowledge, scrape_and_store, get_recent_knowledge

def extract_deepseek_response(content, model_name):
    """Extract the actual response from DeepSeek R1's output, handling reasoning traces"""
    from core.nova_logger import log_debug

    if not content:
        return content

    # DeepSeek R1 often includes reasoning in <think> tags or similar patterns
    # We want to extract the final answer/response

    # Check if this is a DeepSeek R1 model
    if "deepseek-r1" in model_name.lower():
        log_debug("DEEPSEEK_PROCESSING", f"Processing DeepSeek R1 response, length: {len(content)}")

        # DeepSeek R1 may have reasoning traces followed by the actual response
        # Look for common patterns that indicate the start of the final response

        # Pattern 1: Content after reasoning blocks
        if "<think>" in content and "</think>" in content:
            # Extract content after the last </think> tag
            parts = content.split("</think>")
            if len(parts) > 1:
                final_response = parts[-1].strip()
                if final_response:
                    log_debug("DEEPSEEK_PROCESSING", "Extracted response after </think> tag")
                    return final_response

        # Pattern 2: Look for actual conversational response (not reasoning)
        lines = content.split('\n')
        response_lines = []
        in_reasoning = False

        for line in lines:
            line = line.strip()
            if not line:
                continue

            # Skip obvious reasoning patterns
            if any(pattern in line.lower() for pattern in [
                "let me think", "i need to", "first, i", "the user is asking",
                "this is a", "i should", "the question", "to answer this"
            ]):
                in_reasoning = True
                continue

            # Look for direct response patterns
            if any(pattern in line.lower() for pattern in [
                "hello", "hi", "i'm", "i am", "sure", "of course",
                "absolutely", "yes", "no", "here's", "here is"
            ]) and not in_reasoning:
                response_lines.append(line)
                in_reasoning = False
            elif not in_reasoning and len(line) > 10:
                response_lines.append(line)

        if response_lines:
            final_response = '\n'.join(response_lines).strip()
            if final_response and len(final_response) > 5:
                log_debug("DEEPSEEK_PROCESSING", "Extracted conversational response")
                return final_response

        # Pattern 3: If no clear pattern, look for the longest coherent paragraph
        paragraphs = [p.strip() for p in content.split('\n\n') if p.strip()]
        if paragraphs:
            # Find the paragraph that looks most like a direct response
            best_paragraph = ""
            for para in paragraphs:
                if len(para) > len(best_paragraph) and not any(pattern in para.lower() for pattern in [
                    "let me think", "i need to analyze", "first, i should"
                ]):
                    best_paragraph = para

            if best_paragraph:
                log_debug("DEEPSEEK_PROCESSING", "Using best paragraph as response")
                return best_paragraph

    # If no special processing needed or patterns found, return original content
    # But first, clean up any obvious non-conversational text
    cleaned_content = content.strip()

    # Remove any JSON-like artifacts that might have been included
    if cleaned_content.startswith('{') and not cleaned_content.endswith('}'):
        # Looks like incomplete JSON, try to extract readable text
        lines = cleaned_content.split('\n')
        readable_lines = []
        for line in lines:
            line = line.strip()
            if line and not line.startswith('{') and not line.startswith('"') and ':' not in line[:10]:
                readable_lines.append(line)
        if readable_lines:
            cleaned_content = '\n'.join(readable_lines)

    log_debug("DEEPSEEK_PROCESSING", f"Using content (cleaned): {len(cleaned_content)} chars")
    return cleaned_content

def perform_web_search(query, max_results=conf.MAX_SEARCH_RESULTS):
    """Perform web search and return cleaned results"""
    try:
        with DDGS() as ddgs:
            results = []
            dedupe = set()
            
            for result in ddgs.text(query, max_results=max_results*3):
                # Deduplicate by domain
                domain = result.get('hostname', result.get('href','').split('//')[-1].split('/')[0])
                if domain in dedupe:
                    continue
                dedupe.add(domain)
                
                results.append({
                    'title': result.get('title', 'No title'),
                    'snippet': result.get('body', 'No description'),
                    'url': result.get('href', ''),
                    'source': format_source(domain)
                })
                if len(results) >= max_results:
                    break
                    
            return results
    except Exception:
        return []

def create_conversational_summary(results, query):
    """Transform search results into Nova's natural narrative style"""
    if not results:
        return "I couldn't find fresh information on that topic just now. Perhaps try a different phrasing?"
    
    sources = {res['source'] for res in results}
    
    summary_lines = [
        f"🌐 I dug up some fresh info about '{query}' from around the web:",
        ""
    ]
    
    for i, res in enumerate(results, 1):
        summary_lines.append(f"🔹 **{res['source']}**: "
                            f"{truncate_string(res['snippet'], conf.CONVO_TRUNCATE_LENGTH)}")
    
    sources_str = ", ".join(sorted(sources))
    summary_lines.extend([
        "",
        f"📖 *Sources*: {sources_str}",
        "",
        "Would you like me to explore anything specific about what I found?"
    ])
    
    return "\n".join(summary_lines)

def process_tool_request(user_input):
    """Process user input and execute appropriate tools"""
    user_lower = user_input.lower().strip()

    # Enhanced greeting detection - more comprehensive patterns
    greeting_patterns = [
        "hi", "hello", "hey", "how are you", "how you doing", "how are you doing",
        "good morning", "good afternoon", "good evening", "good night",
        "what's up", "sup", "yo", "how's it going", "how you been",
        "how are things", "how's everything", "how's your day",
        "how you doing today", "how are you today", "how's it going today"
    ]

    # Check if this is just a simple greeting (be more lenient with length)
    if any(greeting in user_lower for greeting in greeting_patterns) and len(user_input.strip()) < 50:
        # Additional check: if it's ONLY a greeting without specific requests
        request_keywords = ["weather", "time", "remind", "search", "news", "volume", "open", "close"]
        if not any(keyword in user_lower for keyword in request_keywords):
            return None  # Let normal conversation handle it

    # Weather requests
    if any(phrase in user_lower for phrase in ["weather", "temperature", "forecast", "rain", "sunny", "cloudy"]):
        if "forecast" in user_lower or "tomorrow" in user_lower or "week" in user_lower:
            # Extract city if mentioned
            city = extract_city_from_input(user_input)
            return get_forecast(city)
        else:
            city = extract_city_from_input(user_input)
            return get_weather(city)

    # Time and date requests (be more specific to avoid false matches)
    elif any(phrase in user_lower for phrase in ["what time", "current time", "time is it", "clock"]):
        return get_current_time()
    elif any(phrase in user_lower for phrase in ["what date", "today's date", "date today", "calendar", "what day"]) and not any(phrase in user_lower for phrase in ["how", "doing", "are you"]):
        return get_date_info()
    elif "time until" in user_lower or "how long until" in user_lower:
        # Extract target time from input
        target = user_input.split("until")[-1].strip() if "until" in user_input else ""
        return time_until(target) if target else "Please specify what time you want to count down to!"

    # Reminder requests
    elif any(phrase in user_lower for phrase in ["remind me", "set reminder", "reminder"]):
        if "list" in user_lower or "show" in user_lower:
            return list_reminders()
        elif "remove" in user_lower or "delete" in user_lower:
            # Extract reminder ID
            words = user_input.split()
            for word in words:
                if len(word) >= 8 and word.isalnum():  # Likely a reminder ID
                    return remove_reminder(word)
            return "Please specify the reminder ID to remove."
        else:
            # Parse reminder from input
            return parse_and_add_reminder(user_input)

    # Plan requests
    elif any(phrase in user_lower for phrase in ["save plan", "add plan", "create plan", "plan"]):
        if "list" in user_lower or "show" in user_lower:
            return list_plans()
        else:
            return parse_and_add_plan(user_input)

    # Volume control
    elif any(phrase in user_lower for phrase in ["volume", "sound", "audio"]):
        if "mute" in user_lower:
            return mute_volume()
        elif "unmute" in user_lower:
            return unmute_volume()
        elif any(word.isdigit() for word in user_input.split()):
            # Extract volume level
            for word in user_input.split():
                if word.isdigit():
                    return set_volume(int(word))
        else:
            return get_volume()

    # Application control
    elif any(phrase in user_lower for phrase in ["open", "launch", "start"]) and not "search" in user_lower:
        app_name = extract_app_name(user_input, ["open", "launch", "start"])
        return open_application(app_name) if app_name else "Please specify which application to open."
    elif any(phrase in user_lower for phrase in ["close", "quit", "exit"]) and not user_lower in ["exit", "quit", "bye"]:
        app_name = extract_app_name(user_input, ["close", "quit", "exit"])
        return close_application(app_name) if app_name else "Please specify which application to close."
    elif "running apps" in user_lower or "list apps" in user_lower:
        return list_running_apps()

    # System control
    elif "screenshot" in user_lower or "screen shot" in user_lower:
        return take_screenshot()
    elif "lock computer" in user_lower or "lock screen" in user_lower:
        return lock_computer()
    elif "shutdown" in user_lower:
        return shutdown_computer()
    elif "restart" in user_lower:
        return restart_computer()

    # Knowledge base
    elif any(phrase in user_lower for phrase in ["remember this", "save this", "add to knowledge"]):
        content = user_input.replace("remember this", "").replace("save this", "").replace("add to knowledge", "").strip()
        return add_knowledge("User Note", content)
    elif "search knowledge" in user_lower or "what do you know about" in user_lower:
        query = user_input.replace("search knowledge", "").replace("what do you know about", "").strip()
        return search_knowledge(query) if query else "What would you like me to search for?"
    elif "recent knowledge" in user_lower:
        return get_recent_knowledge()

    # Web search requests
    elif any(phrase in user_lower for phrase in ["search for", "look up", "find information", "web search", "search the web"]):
        query = user_input
        # Clean up the query
        for phrase in ["search for", "look up", "find information about", "web search", "search the web for"]:
            query = query.lower().replace(phrase, "").strip()

        if query:
            search_results = perform_web_search(query)
            return create_conversational_summary(search_results, query)
        else:
            return "What would you like me to search for? 🔍"

    # News requests - be more specific to avoid false matches
    elif any(phrase in user_lower for phrase in ["news", "latest news", "current events", "headlines", "breaking news"]):
        # Make sure it's actually a news request, not just casual conversation
        if any(word in user_lower for word in ["get", "show", "tell", "what", "find", "search"]) or user_lower.startswith("news"):
            # Extract topic if mentioned
            news_query = user_input
            for phrase in ["news about", "latest news on", "news on", "headlines about"]:
                if phrase in user_lower:
                    news_query = user_input.lower().split(phrase)[-1].strip()
                    break
            else:
                # If no specific topic, get general news
                if any(word in user_lower for word in ["latest", "current", "breaking"]) and "news" in user_lower:
                    news_query = "latest news today"
                else:
                    news_query = user_input

            search_results = perform_web_search(news_query)
            return create_conversational_summary(search_results, news_query)

    # If no tool matches, return None to use normal conversation
    return None

def extract_city_from_input(user_input):
    """Extract city name from weather request"""
    # Simple city extraction - can be enhanced
    words = user_input.split()
    prepositions = ["in", "for", "at"]

    for i, word in enumerate(words):
        if word.lower() in prepositions and i + 1 < len(words):
            return " ".join(words[i+1:]).strip("?.,!")

    return None

def extract_app_name(user_input, action_words):
    """Extract application name from open/close request"""
    words = user_input.split()

    for action in action_words:
        if action in user_input.lower():
            parts = user_input.lower().split(action)
            if len(parts) > 1:
                app_name = parts[1].strip()
                # Remove common words
                app_name = app_name.replace("the", "").replace("application", "").replace("app", "").strip()
                return app_name

    return None

def parse_and_add_reminder(user_input):
    """Parse reminder from natural language input"""
    # Simple parsing - can be enhanced with NLP
    try:
        # Look for time patterns
        import re
        from dateutil import parser

        # Common patterns
        time_patterns = [
            r'at (\d{1,2}:\d{2})',
            r'at (\d{1,2} ?[ap]m)',
            r'in (\d+) (minute|hour|day)s?',
            r'(tomorrow|today) at (\d{1,2}:\d{2})',
            r'(tomorrow|today) at (\d{1,2} ?[ap]m)'
        ]

        reminder_text = user_input
        when = None

        for pattern in time_patterns:
            match = re.search(pattern, user_input.lower())
            if match:
                time_str = match.group(0)
                reminder_text = user_input.replace(match.group(0), "").replace("remind me", "").strip()

                try:
                    when = parser.parse(time_str)
                    break
                except:
                    continue

        if when and reminder_text:
            return add_reminder(reminder_text, when)
        else:
            return "I couldn't understand the time format. Try something like 'remind me to call John at 3pm' or 'remind me to take medicine in 2 hours'."

    except Exception as e:
        return f"Sorry, I had trouble parsing that reminder: {str(e)}"

def parse_and_add_plan(user_input):
    """Parse plan from natural language input"""
    # Extract plan name and details
    plan_text = user_input.replace("save plan", "").replace("add plan", "").replace("create plan", "").strip()

    if not plan_text:
        return "Please provide plan details. For example: 'Save plan: Weekend trip to Paris - book flights, hotel, and activities'"

    # Split on common separators
    if ":" in plan_text:
        parts = plan_text.split(":", 1)
        plan_name = parts[0].strip()
        plan_details = parts[1].strip() if len(parts) > 1 else ""
    elif "-" in plan_text:
        parts = plan_text.split("-", 1)
        plan_name = parts[0].strip()
        plan_details = parts[1].strip() if len(parts) > 1 else ""
    else:
        plan_name = plan_text[:50] + "..." if len(plan_text) > 50 else plan_text
        plan_details = plan_text

    return add_plan(plan_name, plan_details)

# Chat Engine Integration
def get_simple_deepseek_response(prompt):
    """Get a simple response from DeepSeek using a single prompt - no complex JSON parsing"""
    from core.nova_logger import log_api_call, log_error, log_debug

    headers = {
        "Authorization": f"Bearer {OPENROUTER_API_KEY}",
        "HTTP-Referer": conf.WEBSITE_URL,
        "X-Title": conf.APP_NAME,
        "Content-Type": "application/json"
    }

    payload = {
        "model": conf.DEEPSEEK_MODEL,
        "messages": [{"role": "user", "content": prompt}],
        "temperature": 0.8,
        "max_tokens": 1500,
        "stream": False
    }

    log_debug("SIMPLE_API", f"Making simple request to {conf.DEEPSEEK_MODEL}")

    try:
        response = requests.post(conf.OPENROUTER_URL, headers=headers, json=payload, timeout=30)

        if response.status_code == 200:
            # Try JSON parsing first
            try:
                response_data = response.json()
                if 'choices' in response_data and response_data['choices']:
                    content = response_data['choices'][0].get('message', {}).get('content', '')
                    if content:
                        return content.strip()
            except:
                pass

            # If JSON fails, try to extract any readable text from the response
            raw_text = response.text.strip()
            if raw_text and len(raw_text) > 10:
                # Look for any text that looks like a response
                if not raw_text.startswith('{'):
                    return raw_text
                else:
                    # Try to find readable text in the response
                    lines = raw_text.split('\n')
                    for line in lines:
                        line = line.strip()
                        if line and not line.startswith('{') and not line.startswith('"') and len(line) > 20:
                            return line

        log_error("SIMPLE_API", f"Failed to get response: {response.status_code}")
        return None

    except Exception as e:
        log_error("SIMPLE_API", f"Error: {e}")
        return None


def get_chat_response(messages):
    """Get response from OpenRouter API with fallback support"""
    from core.nova_logger import log_api_call, log_error, log_debug

    # Validate API key
    if not OPENROUTER_API_KEY or OPENROUTER_API_KEY == "YOUR_OPENROUTER_API_KEY_HERE":
        log_error("API_KEY", "OpenRouter API key not configured")
        return "I need an API key to be configured. Please check the setup guide."

    # Validate messages
    if not messages or not isinstance(messages, list):
        log_error("API_MESSAGES", "Invalid messages format")
        return "I'm having trouble processing your request. Please try again."

    # For simple single-user prompts, use the simple method
    if len(messages) == 1 and messages[0]['role'] == 'user':
        log_debug("API", "Using simple API method for single prompt")
        simple_response = get_simple_deepseek_response(messages[0]['content'])
        if simple_response:
            return simple_response

    headers = {
        "Authorization": f"Bearer {OPENROUTER_API_KEY}",
        "HTTP-Referer": conf.WEBSITE_URL,
        "X-Title": conf.APP_NAME,
        "Content-Type": "application/json"
    }

    log_debug("API", f"Using API key: {OPENROUTER_API_KEY[:10]}...{OPENROUTER_API_KEY[-5:]}")
    log_debug("API", f"Request messages: {[msg['role'] for msg in messages]}")

    # Try primary model first, then fallbacks
    models_to_try = [conf.DEEPSEEK_MODEL] + conf.FALLBACK_MODELS

    for model_name in models_to_try:
        log_debug("API", f"Trying model: {model_name}")

        # Adjust parameters for DeepSeek R1 model
        if "deepseek-r1" in model_name:
            payload = {
                "model": model_name,
                "messages": messages,
                "temperature": 0.8,  # Slightly higher for more natural responses
                "max_tokens": 1500,  # More tokens for detailed responses
                "top_p": 0.9,
                "frequency_penalty": 0.1
            }
        else:
            payload = {
                "model": model_name,
                "messages": messages,
                "temperature": 0.7,
                "max_tokens": 1000
            }

        log_debug("API", f"Making API call with {len(messages)} messages")

        try:
            response = requests.post(conf.OPENROUTER_URL, headers=headers, json=payload, timeout=30)
            log_debug("API", f"Response status: {response.status_code}")
            log_debug("API", f"Response headers: {dict(response.headers)}")

            # If successful, process the response
            if response.status_code == 200:
                try:
                    # Check if response has content before parsing
                    if not response.text or response.text.strip() == "":
                        log_error("API_EMPTY", f"Received empty response from {model_name}")
                        continue  # Try next model

                    log_debug("API", f"Raw response text (first 500 chars): {response.text[:500]}")

                    # Special handling for DeepSeek R1 - it might return non-JSON responses
                    if "deepseek-r1" in model_name.lower():
                        # Try to handle potential non-JSON responses from DeepSeek R1
                        try:
                            response_data = response.json()
                        except json.JSONDecodeError:
                            # If JSON parsing fails, try to use the raw text as the response
                            log_debug("DEEPSEEK_RAW", "JSON failed, attempting raw text processing")
                            raw_text = response.text.strip()
                            if raw_text and len(raw_text) > 5:
                                processed_content = extract_deepseek_response(raw_text, model_name)
                                if processed_content:
                                    log_api_call(model_name, 200, processed_content)
                                    return processed_content
                            # If raw processing fails, continue to next model
                            log_error("DEEPSEEK_RAW_FAILED", f"Raw text processing failed for {model_name}")
                            continue
                    else:
                        response_data = response.json()

                    log_debug("API", f"Response data keys: {list(response_data.keys())}")

                    # Check if response has the expected structure
                    if 'choices' in response_data and len(response_data['choices']) > 0:
                        choice = response_data['choices'][0]

                        # Handle DeepSeek R1 specific response format
                        if 'message' in choice and 'content' in choice['message']:
                            content = choice['message']['content']

                            # DeepSeek R1 may include reasoning traces - extract the final answer
                            if content and content.strip():
                                # For DeepSeek R1, the content might have reasoning traces
                                # Look for the actual response after reasoning
                                processed_content = extract_deepseek_response(content, model_name)
                                log_api_call(model_name, 200, processed_content)
                                return processed_content
                            else:
                                log_error("API_EMPTY_CONTENT", f"Received empty content from {model_name}")
                                continue  # Try next model
                        else:
                            log_error("API_STRUCTURE", f"Missing message/content in choice from {model_name}")
                            continue  # Try next model
                    else:
                        log_error("API_STRUCTURE", f"Unexpected response structure from {model_name}: {response_data}")
                        continue  # Try next model

                except json.JSONDecodeError as e:
                    log_error("API_JSON", f"JSON decode error from {model_name}: {e}")
                    log_error("API_JSON_RESPONSE", f"Response text length: {len(response.text)}")
                    log_error("API_JSON_RESPONSE", f"First 1000 chars: {response.text[:1000]}")

                    # Check if this is an empty response issue
                    if not response.text or response.text.strip() == "":
                        log_error("API_EMPTY_AFTER_200", f"Empty response despite 200 status from {model_name}")
                        continue  # Try next model

                    # Special handling for DeepSeek R1 - sometimes it returns plain text instead of JSON
                    if "deepseek-r1" in model_name.lower() and response.text.strip():
                        log_debug("DEEPSEEK_FALLBACK", "Attempting to use raw response as content")
                        try:
                            # Use the raw response as content if it looks like a valid response
                            raw_content = response.text.strip()
                            if len(raw_content) > 5 and not raw_content.startswith('{'):
                                processed_content = extract_deepseek_response(raw_content, model_name)
                                log_api_call(model_name, 200, processed_content)
                                return processed_content
                        except Exception as fallback_error:
                            log_error("DEEPSEEK_FALLBACK", f"Fallback processing failed: {fallback_error}")

                    # Try to extract any readable content from malformed response
                    try:
                        # Sometimes the response might have extra content before/after JSON
                        text = response.text.strip()
                        if text.startswith('{') and text.endswith('}'):
                            # Try to find the JSON part
                            start = text.find('{"')
                            end = text.rfind('}') + 1
                            if start >= 0 and end > start:
                                json_part = text[start:end]
                                response_data = json.loads(json_part)
                                if 'choices' in response_data and len(response_data['choices']) > 0:
                                    choice = response_data['choices'][0]
                                    if 'message' in choice and 'content' in choice['message']:
                                        content = choice['message']['content']
                                        if content and content.strip():
                                            log_api_call(model_name, 200, content)
                                            return content
                    except Exception as recovery_error:
                        log_error("API_JSON_RECOVERY", f"Recovery attempt failed: {recovery_error}")
                    continue  # Try next model
            else:
                log_error("API_STATUS", f"{model_name} returned status {response.status_code}: {response.text[:200]}")
                continue  # Try next model

        except requests.exceptions.Timeout:
            log_error("API_TIMEOUT", f"Request to {model_name} timed out after 30 seconds")
            continue  # Try next model
        except requests.exceptions.ConnectionError as e:
            log_error("API_CONNECTION", f"Connection error with {model_name}: {e}")
            continue  # Try next model
        except Exception as e:
            log_error("API_UNEXPECTED", f"Unexpected error with {model_name}: {e}")
            continue  # Try next model

    # If all models failed, return a fallback message
    log_error("API_ALL_FAILED", "All models failed to respond")
    return "I'm experiencing some technical difficulties right now. Could you try asking again in a moment? 🤔"


def get_tool_data(user_input):
    """Execute tools and return raw data instead of formatted responses"""
    user_lower = user_input.lower().strip()

    # Enhanced greeting detection - same as process_tool_request
    greeting_patterns = [
        "hi", "hello", "hey", "how are you", "how you doing", "how are you doing",
        "good morning", "good afternoon", "good evening", "good night",
        "what's up", "sup", "yo", "how's it going", "how you been",
        "how are things", "how's everything", "how's your day",
        "how you doing today", "how are you today", "how's it going today"
    ]

    # Check if this is just a simple greeting
    if any(greeting in user_lower for greeting in greeting_patterns) and len(user_input.strip()) < 50:
        request_keywords = ["weather", "time", "remind", "search", "news", "volume", "open", "close"]
        if not any(keyword in user_lower for keyword in request_keywords):
            return None, None  # No tool data

    # Weather requests - return raw data
    if any(phrase in user_lower for phrase in ["weather", "temperature", "forecast", "rain", "sunny", "cloudy"]):
        if "forecast" in user_lower or "tomorrow" in user_lower or "week" in user_lower:
            city = extract_city_from_input(user_input)
            forecast_data = get_forecast(city)
            return "weather_forecast", forecast_data
        else:
            city = extract_city_from_input(user_input)
            weather_data = get_weather(city)
            return "weather_current", weather_data

    # Time and date requests
    elif any(phrase in user_lower for phrase in ["what time", "current time", "time is it", "clock"]):
        time_data = get_current_time()
        return "current_time", time_data
    elif any(phrase in user_lower for phrase in ["what date", "today's date", "date today", "calendar", "what day"]) and not any(phrase in user_lower for phrase in ["how", "doing", "are you"]):
        date_data = get_date_info()
        return "current_date", date_data

    # Web search requests
    elif any(phrase in user_lower for phrase in ["search", "look up", "find", "google"]):
        search_query = user_input
        for phrase in ["search for", "look up", "find", "google"]:
            if phrase in user_lower:
                search_query = user_input.lower().split(phrase)[-1].strip()
                break
        search_results = perform_web_search(search_query)
        return "web_search", {"query": search_query, "results": search_results}

    # News requests
    elif any(phrase in user_lower for phrase in ["news", "latest news", "current events", "headlines", "breaking news"]):
        if any(word in user_lower for word in ["get", "show", "tell", "what", "find", "search"]) or user_lower.startswith("news"):
            news_query = user_input
            for phrase in ["news about", "latest news on", "news on", "headlines about"]:
                if phrase in user_lower:
                    news_query = user_input.lower().split(phrase)[-1].strip()
                    break
            else:
                if any(word in user_lower for word in ["latest", "current", "breaking"]) and "news" in user_lower:
                    news_query = "latest news today"
                else:
                    news_query = user_input
            search_results = perform_web_search(news_query)
            return "news_search", {"query": news_query, "results": search_results}

    return None, None  # No tool data


def create_tool_prompt(user_input, tool_type, tool_data):
    """Create a custom prompt that includes tool data for DeepSeek to process conversationally"""

    if tool_type == "weather_current":
        return f"""The user asked: "{user_input}"

I just checked the current weather and here's what I found: {tool_data}

Please respond naturally as Nova, a friendly AI assistant, incorporating this weather information into a conversational response. Be warm and helpful."""

    elif tool_type == "weather_forecast":
        return f"""The user asked: "{user_input}"

I just checked the weather forecast and here's what I found: {tool_data}

Please respond naturally as Nova, a friendly AI assistant, incorporating this forecast information into a conversational response. Be warm and helpful."""

    elif tool_type == "current_time":
        return f"""The user asked: "{user_input}"

I just checked the current time and here's what I found: {tool_data}

Please respond naturally as Nova, a friendly AI assistant, incorporating this time information into a conversational response. Be warm and helpful."""

    elif tool_type == "current_date":
        return f"""The user asked: "{user_input}"

I just checked the current date and here's what I found: {tool_data}

Please respond naturally as Nova, a friendly AI assistant, incorporating this date information into a conversational response. Be warm and helpful."""

    elif tool_type == "web_search":
        results_text = ""
        for i, result in enumerate(tool_data["results"][:4], 1):
            results_text += f"{i}. {result['title']}\n   {result['snippet'][:150]}...\n   Source: {result['source']}\n\n"

        return f"""The user asked: "{user_input}"

I just searched the web for "{tool_data['query']}" and found these results:

{results_text}

Please respond naturally as Nova, a friendly AI assistant, summarizing this information in a conversational way. Be warm, helpful, and mention the sources naturally."""

    elif tool_type == "news_search":
        results_text = ""
        for i, result in enumerate(tool_data["results"][:4], 1):
            results_text += f"{i}. {result['title']}\n   {result['snippet'][:150]}...\n   Source: {result['source']}\n\n"

        return f"""The user asked: "{user_input}"

I just searched for the latest news about "{tool_data['query']}" and found these results:

{results_text}

Please respond naturally as Nova, a friendly AI assistant, sharing this news in a conversational way. Be warm, helpful, and mention the sources naturally."""

    else:
        return f"""The user asked: "{user_input}"

I used a tool to get this information: {tool_data}

Please respond naturally as Nova, a friendly AI assistant, incorporating this information into a conversational response. Be warm and helpful."""


def process_with_deepseek(user_input, tool_type, tool_data, memory):
    """Process tool data through DeepSeek using custom prompts instead of JSON"""
    from core.nova_logger import log_debug

    # Create a custom prompt that includes the tool data
    tool_prompt = create_tool_prompt(user_input, tool_type, tool_data)

    log_debug("DEEPSEEK_TOOL", f"Processing {tool_type} with custom prompt")
    log_debug("DEEPSEEK_PROMPT", f"Custom prompt: {tool_prompt[:200]}...")

    # Use the simple API method to avoid JSON parsing issues
    response = get_simple_deepseek_response(tool_prompt)

    if response:
        return response
    else:
        # Fallback response if API fails
        log_debug("DEEPSEEK_FALLBACK", "Using fallback response")
        return f"I got some information about your request, but I'm having trouble processing it right now. Could you try asking again? 🤔"
