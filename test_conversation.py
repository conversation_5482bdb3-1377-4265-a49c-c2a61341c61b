# test_conversation.py
"""
Test the DeepSeek R1 conversation functionality
"""

from core import nova_tools

def test_simple_conversation():
    """Test simple conversation"""
    print("🧪 Testing Simple Conversation...")
    
    response = nova_tools.process_user_input("Hello Nova, how are you?")
    
    if response and len(response) > 5:
        print("✅ Simple conversation works!")
        print(f"💬 Response: {response}")
        return True
    else:
        print("❌ Simple conversation failed")
        return False

def test_weather_conversation():
    """Test weather tool with conversation"""
    print("\n🧪 Testing Weather Tool Conversation...")
    
    response = nova_tools.process_user_input("What's the weather like?")
    
    if response and len(response) > 10:
        print("✅ Weather conversation works!")
        print(f"🌤️ Response: {response}")
        return True
    else:
        print("❌ Weather conversation failed")
        return False

def test_time_conversation():
    """Test time tool with conversation"""
    print("\n🧪 Testing Time Tool Conversation...")
    
    response = nova_tools.process_user_input("What time is it?")
    
    if response and len(response) > 10:
        print("✅ Time conversation works!")
        print(f"⏰ Response: {response}")
        return True
    else:
        print("❌ Time conversation failed")
        return False

def test_greeting_detection():
    """Test that greetings don't trigger tools unnecessarily"""
    print("\n🧪 Testing Greeting Detection...")

    response = nova_tools.process_user_input("How are you doing today?")

    if response and "weather" not in response.lower():
        print("✅ Greeting handled correctly (no weather tool triggered)")
        print(f"👋 Response: {response}")
        return True
    else:
        print("❌ Greeting incorrectly triggered tools")
        return False

def test_scraping_conversation():
    """Test web scraping tool with conversation"""
    print("\n🧪 Testing Web Scraping Tool Conversation...")

    response = nova_tools.process_user_input("Scrape https://httpbin.org/html")

    if response and len(response) > 10:
        print("✅ Web scraping conversation works!")
        print(f"🔍 Response: {response[:200]}...")
        return True
    else:
        print("❌ Web scraping conversation failed")
        return False

if __name__ == "__main__":
    print("🌟 Nova Conversation Test")
    print("=" * 40)
    
    # Check API key
    try:
        from config.api_keys import OPENROUTER_API_KEY
        if not OPENROUTER_API_KEY or OPENROUTER_API_KEY == "YOUR_OPENROUTER_API_KEY_HERE":
            print("❌ API key not configured!")
            exit(1)
        print(f"✅ API key configured")
    except:
        print("❌ API key file not found!")
        exit(1)
    
    tests_passed = 0
    total_tests = 5

    try:
        if test_simple_conversation():
            tests_passed += 1

        if test_greeting_detection():
            tests_passed += 1

        if test_weather_conversation():
            tests_passed += 1

        if test_time_conversation():
            tests_passed += 1

        if test_scraping_conversation():
            tests_passed += 1
            
    except Exception as e:
        print(f"❌ Test error: {e}")
        import traceback
        traceback.print_exc()
    
    print("\n" + "=" * 40)
    print(f"📊 Results: {tests_passed}/{total_tests} tests passed")
    
    if tests_passed >= 3:
        print("🎉 Conversation system is working!")
        print("✨ Try running: python main.py")
    else:
        print("⚠️  Some tests failed.")
