# test_simplified_nova.py
"""
Test the simplified Nova approach with custom prompts
"""

from core import nova_tools

def test_simple_conversation():
    """Test simple conversation"""
    print("🧪 Testing Simple Conversation...")
    
    prompt = """You are <PERSON>, a warm and friendly AI assistant. Here's our recent conversation:

User: Hello <PERSON>, how are you?

Please respond naturally as <PERSON>. Be warm, helpful, and conversational."""

    response = nova_tools.get_simple_deepseek_response(prompt)
    
    if response and len(response) > 5:
        print("✅ Simple conversation successful!")
        print(f"💬 Response: {response}")
        return True
    else:
        print("❌ Simple conversation failed")
        return False

def test_weather_tool():
    """Test weather tool with custom prompt"""
    print("\n🧪 Testing Weather Tool...")
    
    # Test tool detection
    tool_type, tool_data = nova_tools.get_tool_data("What's the weather like?")
    
    if tool_type == "weather_current":
        print("✅ Weather tool detected correctly")
        
        # Mock weather data for testing
        mock_weather = "Temperature: 22°C, Condition: Partly cloudy, Humidity: 65%, Wind: 10 km/h, Location: London"
        
        # Test custom prompt creation
        prompt = nova_tools.create_tool_prompt("What's the weather like?", "weather_current", mock_weather)
        print(f"📝 Generated prompt: {prompt[:150]}...")
        
        # Test response
        response = nova_tools.get_simple_deepseek_response(prompt)
        
        if response and len(response) > 10:
            print("✅ Weather tool response successful!")
            print(f"🌤️ Response: {response}")
            return True
        else:
            print("❌ Weather tool response failed")
            return False
    else:
        print(f"❌ Weather tool detection failed: {tool_type}")
        return False

def test_greeting_detection():
    """Test that greetings don't trigger tools"""
    print("\n🧪 Testing Greeting Detection...")
    
    greetings = [
        "Hello Nova",
        "Hi there",
        "How are you doing today",
        "Good morning"
    ]
    
    all_passed = True
    for greeting in greetings:
        tool_type, tool_data = nova_tools.get_tool_data(greeting)
        if tool_type is None:
            print(f"✅ '{greeting}' correctly ignored")
        else:
            print(f"❌ '{greeting}' incorrectly triggered: {tool_type}")
            all_passed = False
    
    return all_passed

def test_time_tool():
    """Test time tool"""
    print("\n🧪 Testing Time Tool...")
    
    tool_type, tool_data = nova_tools.get_tool_data("What time is it?")
    
    if tool_type == "current_time":
        print("✅ Time tool detected correctly")
        
        # Mock time data
        mock_time = "Current time: 3:45 PM EST, Tuesday, January 9, 2025"
        
        prompt = nova_tools.create_tool_prompt("What time is it?", "current_time", mock_time)
        response = nova_tools.get_simple_deepseek_response(prompt)
        
        if response and len(response) > 10:
            print("✅ Time tool response successful!")
            print(f"⏰ Response: {response}")
            return True
        else:
            print("❌ Time tool response failed")
            return False
    else:
        print(f"❌ Time tool detection failed: {tool_type}")
        return False

if __name__ == "__main__":
    print("🌟 Nova AI Agent - Simplified Approach Test")
    print("=" * 60)
    
    # Check API key
    try:
        from config.api_keys import OPENROUTER_API_KEY
        if not OPENROUTER_API_KEY or OPENROUTER_API_KEY == "YOUR_OPENROUTER_API_KEY_HERE":
            print("❌ API key not configured!")
            exit(1)
        print(f"✅ API key configured: {OPENROUTER_API_KEY[:10]}...")
    except:
        print("❌ API key file not found!")
        exit(1)
    
    tests_passed = 0
    total_tests = 4
    
    try:
        if test_simple_conversation():
            tests_passed += 1
        
        if test_greeting_detection():
            tests_passed += 1
            
        if test_weather_tool():
            tests_passed += 1
            
        if test_time_tool():
            tests_passed += 1
            
    except Exception as e:
        print(f"❌ Test error: {e}")
        import traceback
        traceback.print_exc()
    
    print("\n" + "=" * 60)
    print(f"📊 Results: {tests_passed}/{total_tests} tests passed")
    
    if tests_passed >= 3:
        print("🎉 Simplified approach is working!")
        print("✨ Nova should now work without JSON parsing issues")
        print("\n🚀 Try running Nova with: python main.py")
    else:
        print("⚠️  Some tests failed. Check the configuration.")
