# test_clean_nova.py
"""
Test the clean Nova setup
"""

from core import nova_tools

def test_simple_response():
    """Test simple response"""
    print("🧪 Testing Simple Response...")
    
    prompt = "You are <PERSON>, a friendly AI assistant. The user said 'Hello Nova, how are you?' Please respond warmly."
    
    response = nova_tools.get_simple_deepseek_response(prompt)
    
    if response and len(response) > 5:
        print("✅ Simple response works!")
        print(f"💬 Response: {response}")
        return True
    else:
        print("❌ Simple response failed")
        return False

def test_greeting_detection():
    """Test greeting detection"""
    print("\n🧪 Testing Greeting Detection...")
    
    tool_type, tool_data = nova_tools.get_tool_data("Hello Nova, how are you?")
    
    if tool_type is None and tool_data is None:
        print("✅ Greeting correctly ignored")
        return True
    else:
        print(f"❌ Greeting incorrectly triggered: {tool_type}")
        return False

def test_weather_detection():
    """Test weather detection"""
    print("\n🧪 Testing Weather Detection...")
    
    tool_type, tool_data = nova_tools.get_tool_data("What's the weather like?")
    
    if tool_type == "weather_current":
        print("✅ Weather correctly detected")
        return True
    else:
        print(f"❌ Weather detection failed: {tool_type}")
        return False

if __name__ == "__main__":
    print("🌟 Clean Nova Test")
    print("=" * 40)
    
    tests_passed = 0
    total_tests = 3
    
    try:
        if test_simple_response():
            tests_passed += 1
        
        if test_greeting_detection():
            tests_passed += 1
            
        if test_weather_detection():
            tests_passed += 1
            
    except Exception as e:
        print(f"❌ Test error: {e}")
        import traceback
        traceback.print_exc()
    
    print("\n" + "=" * 40)
    print(f"📊 Results: {tests_passed}/{total_tests} tests passed")
    
    if tests_passed == total_tests:
        print("🎉 Clean Nova is working!")
        print("✨ Try running: python main.py")
    else:
        print("⚠️  Some tests failed.")
