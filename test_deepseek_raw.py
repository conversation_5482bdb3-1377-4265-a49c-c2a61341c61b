# test_deepseek_raw.py
"""
Raw test of DeepSeek R1 API to see exactly what it returns
"""

import requests
import json
from config import nova_config as conf
from config.api_keys import OPENROUTER_API_KEY

def test_deepseek_raw():
    """Test DeepSeek R1 with minimal setup"""
    print("🧪 Testing DeepSeek R1 Raw Response...")
    
    headers = {
        "Authorization": f"Bearer {OPENROUTER_API_KEY}",
        "HTTP-Referer": conf.WEBSITE_URL,
        "X-Title": conf.APP_NAME,
        "Content-Type": "application/json"
    }
    
    # Simple test message
    payload = {
        "model": "deepseek/deepseek-r1:free",
        "messages": [
            {"role": "system", "content": "You are <PERSON>, a helpful AI assistant."},
            {"role": "user", "content": "Hello! Just say 'Hi there!' and nothing else."}
        ],
        "temperature": 0.8,
        "max_tokens": 50
    }
    
    print(f"📤 Sending request to: {conf.OPENROUTER_URL}")
    print(f"🤖 Model: {payload['model']}")
    print(f"💬 Message: {payload['messages'][-1]['content']}")
    
    try:
        response = requests.post(conf.OPENROUTER_URL, headers=headers, json=payload, timeout=30)
        
        print(f"\n📊 Response Status: {response.status_code}")
        print(f"📋 Response Headers:")
        for key, value in response.headers.items():
            print(f"   {key}: {value}")
        
        print(f"\n📄 Raw Response Text:")
        print(f"Length: {len(response.text)} characters")
        print("=" * 50)
        print(response.text)
        print("=" * 50)
        
        if response.status_code == 200:
            try:
                print(f"\n🔍 Attempting JSON Parse...")
                response_data = response.json()
                print(f"✅ JSON Parse Successful!")
                print(f"🔑 Keys: {list(response_data.keys())}")
                
                if 'choices' in response_data:
                    print(f"📝 Choices: {len(response_data['choices'])}")
                    if response_data['choices']:
                        choice = response_data['choices'][0]
                        print(f"🎯 Choice keys: {list(choice.keys())}")
                        
                        if 'message' in choice:
                            message = choice['message']
                            print(f"💌 Message keys: {list(message.keys())}")
                            
                            if 'content' in message:
                                content = message['content']
                                print(f"📝 Content: '{content}'")
                                print(f"📏 Content length: {len(content)}")
                                return True
                            else:
                                print("❌ No 'content' in message")
                        else:
                            print("❌ No 'message' in choice")
                    else:
                        print("❌ Empty choices array")
                else:
                    print("❌ No 'choices' in response")
                    
            except json.JSONDecodeError as e:
                print(f"❌ JSON Parse Failed: {e}")
                print(f"🔍 Error position: line {e.lineno}, column {e.colno}")
                
                # Try to find where the JSON might be malformed
                if response.text:
                    lines = response.text.split('\n')
                    print(f"📄 Response lines ({len(lines)}):")
                    for i, line in enumerate(lines[:10], 1):  # Show first 10 lines
                        print(f"   {i}: {repr(line)}")
                    if len(lines) > 10:
                        print(f"   ... and {len(lines) - 10} more lines")
                
                return False
        else:
            print(f"❌ HTTP Error: {response.status_code}")
            return False
            
    except requests.exceptions.Timeout:
        print("❌ Request timed out")
        return False
    except Exception as e:
        print(f"❌ Unexpected error: {e}")
        return False

def test_with_different_params():
    """Test with different parameters"""
    print("\n🧪 Testing with different parameters...")
    
    test_configs = [
        {"temperature": 0.1, "max_tokens": 20, "description": "Low temp, short"},
        {"temperature": 0.7, "max_tokens": 100, "description": "Medium temp, medium"},
        {"temperature": 1.0, "max_tokens": 200, "description": "High temp, long"},
    ]
    
    for config in test_configs:
        print(f"\n🔧 Testing: {config['description']}")
        
        headers = {
            "Authorization": f"Bearer {OPENROUTER_API_KEY}",
            "HTTP-Referer": conf.WEBSITE_URL,
            "X-Title": conf.APP_NAME,
            "Content-Type": "application/json"
        }
        
        payload = {
            "model": "deepseek/deepseek-r1:free",
            "messages": [
                {"role": "user", "content": "Say hello"}
            ],
            "temperature": config["temperature"],
            "max_tokens": config["max_tokens"]
        }
        
        try:
            response = requests.post(conf.OPENROUTER_URL, headers=headers, json=payload, timeout=15)
            
            if response.status_code == 200:
                try:
                    response_data = response.json()
                    if 'choices' in response_data and response_data['choices']:
                        content = response_data['choices'][0].get('message', {}).get('content', '')
                        print(f"   ✅ Success: '{content[:50]}{'...' if len(content) > 50 else ''}'")
                    else:
                        print(f"   ⚠️  No content in response")
                except json.JSONDecodeError:
                    print(f"   ❌ JSON error, response length: {len(response.text)}")
            else:
                print(f"   ❌ HTTP {response.status_code}")
                
        except Exception as e:
            print(f"   ❌ Error: {e}")

if __name__ == "__main__":
    print("🌟 DeepSeek R1 Raw Response Test")
    print("=" * 50)
    
    if not OPENROUTER_API_KEY or OPENROUTER_API_KEY == "YOUR_OPENROUTER_API_KEY_HERE":
        print("❌ API key not configured!")
        exit(1)
    
    success = test_deepseek_raw()
    
    if not success:
        test_with_different_params()
    
    print("\n" + "=" * 50)
    print("Test completed.")
